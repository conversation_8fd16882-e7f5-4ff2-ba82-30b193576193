#ifndef __Software_H
#define __Software_H

#include "Hardware.h"

// USART缓冲区大小定义
#define USART1_SEND_BUFFER_SIZE     10
#define USART1_RECEIVE_BUFFER_SIZE  10
#define USART2_SEND_BUFFER_SIZE     10
#define USART2_RECEIVE_BUFFER_SIZE  10
#define USART3_SEND_BUFFER_SIZE     10
#define USART3_RECEIVE_BUFFER_SIZE  10
#define USART4_SEND_BUFFER_SIZE     10
#define USART4_RECEIVE_BUFFER_SIZE  10
#define USART5_SEND_BUFFER_SIZE     10
#define USART5_RECEIVE_BUFFER_SIZE  10
#define USART6_SEND_BUFFER_SIZE     10
#define USART6_RECEIVE_BUFFER_SIZE  10
#define USART7_SEND_BUFFER_SIZE     10
#define USART7_RECEIVE_BUFFER_SIZE  10
#define USART8_SEND_BUFFER_SIZE     10
#define USART8_RECEIVE_BUFFER_SIZE  10

// USART1管理结构体 - H2传感器
typedef struct
{
	uint8_t sendBuffer[USART1_SEND_BUFFER_SIZE];
	uint8_t receiveBuffer[USART1_RECEIVE_BUFFER_SIZE];
}usart1_management_t;
extern usart1_management_t usart1_management;

// USART2管理结构体 - CO传感器
typedef struct
{
	uint8_t sendBuffer[USART2_SEND_BUFFER_SIZE];
	uint8_t receiveBuffer[USART2_RECEIVE_BUFFER_SIZE];
}usart2_management_t;
extern usart2_management_t usart2_management;

// USART3管理结构体 - H2传感器
typedef struct
{
	uint8_t sendBuffer[USART3_SEND_BUFFER_SIZE];
	uint8_t receiveBuffer[USART3_RECEIVE_BUFFER_SIZE];
}usart3_management_t;
extern usart3_management_t usart3_management;

// USART4管理结构体 - 膨胀力传感器1
typedef struct
{
	uint8_t sendBuffer[USART4_SEND_BUFFER_SIZE];
	uint8_t receiveBuffer[USART4_RECEIVE_BUFFER_SIZE];
}usart4_management_t;
extern usart4_management_t usart4_management;

// USART5管理结构体 - 膨胀力传感器2
typedef struct
{
	uint8_t sendBuffer[USART5_SEND_BUFFER_SIZE];
	uint8_t receiveBuffer[USART5_RECEIVE_BUFFER_SIZE];
}usart5_management_t;
extern usart5_management_t usart5_management;

// USART6管理结构体 - 膨胀力传感器4
typedef struct
{
	uint8_t sendBuffer[USART6_SEND_BUFFER_SIZE];
	uint8_t receiveBuffer[USART6_RECEIVE_BUFFER_SIZE];
}usart6_management_t;
extern usart6_management_t usart6_management;

// USART7管理结构体 - O2传感器
typedef struct
{
	uint8_t sendBuffer[USART7_SEND_BUFFER_SIZE];
	uint8_t receiveBuffer[USART7_RECEIVE_BUFFER_SIZE];
}usart7_management_t;
extern usart7_management_t usart7_management;

// USART8管理结构体 - CO2传感器
typedef struct
{
	uint8_t sendBuffer[USART8_SEND_BUFFER_SIZE];
	uint8_t receiveBuffer[USART8_RECEIVE_BUFFER_SIZE];
}usart8_management_t;
extern usart8_management_t usart8_management;

// 数据管理结构体
typedef struct
{
	uint16_t co_concentration;      // CO浓度
	uint16_t h2_concentration;      // H2浓度
	uint16_t o2_concentration;      // O2浓度
	uint16_t co2_concentration;     // CO2浓度
	uint32_t expansion_force1;      // 膨胀力1
	uint32_t expansion_force2;      // 膨胀力2
	uint32_t expansion_force3;      // 膨胀力3
	uint32_t expansion_force4;      // 膨胀力4
	uint8_t concentration[8];       // 兼容原有代码
}data_management_t;
extern data_management_t data_management;

// 定时器管理结构体
typedef struct
{
	uint8_t count;
	uint8_t flag_100ms;
	uint8_t flag_900ms;
	uint8_t flag_1s;
}timer2_management_t;
extern timer2_management_t timer2_management;

void software_initializes(void);

#endif

