#include "Usart1.h"

usart1_management_t usart1_management;

/******************************************************************
 * @brief  ��ʼ������1�������жϼ�DMA������ݽ��պͽ���
 *         ����DMA���ͣ���������Զ���������
 * @input  ��
 * @return ��
******************************************************************/
void usart1_initializes(void)
{
	USART_InitTypeDef USART_InitStructure;
	GPIO_InitTypeDef  GPIO_InitStructure;
	NVIC_InitTypeDef  NVIC_InitStructure;
	
	// 膨胀力传感器3：TX连接PB7、RX连接PB6
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);
	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOB, ENABLE);

	GPIO_PinAFConfig(GPIOB, GPIO_PinSource7, GPIO_AF_0);
	GPIO_PinAFConfig(GPIOB, GPIO_PinSource6, GPIO_AF_0);

	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_7 | GPIO_Pin_6;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOB, &GPIO_InitStructure);

	USART_OverSampling8Cmd(USART1, ENABLE);
	USART_InitStructure.USART_BaudRate = 9600;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART1, &USART_InitStructure);
    
	/* usart 1 */
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);

	USART_ITConfig(USART1, USART_IT_IDLE, ENABLE);
	USART_DMACmd(USART1, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);
	USART_Cmd(USART1, ENABLE);
	USART_ClearFlag(USART1, USART_FLAG_TC);
	
	usart_DMA_Config_TX(DMA1_Channel2, (uint32_t)&USART1->TDR, (uint32_t)usart1_management.sendBuffer, USART1_SEND_BUFFER_SIZE);
	usart_DMA_Config_RX(DMA1_Channel3, (uint32_t)&USART1->RDR, (uint32_t)usart1_management.receiveBuffer, USART1_RECEIVE_BUFFER_SIZE);
}

/******************************************************************
 * @brief  ���ô��ڵ�DMA����ͨ��
 * @input  DMA_CHx:ѡ��DMA��ͨ��
 *         cmar��  Ҫ���͵�������ʼ��ַ
 *         cndtr�� ��������С
 *         cndtr:  ���ͻ��泤��
 * @return ��
******************************************************************/
void usart_DMA_Config_TX(DMA_Channel_TypeDef *DMA_CHx, uint32_t cpar, uint32_t cmar, uint16_t cndtr)
{
	DMA_InitTypeDef DMA_InitStructure;
	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_DMA1, ENABLE);

	DMA_InitStructure.DMA_PeripheralBaseAddr = cpar;
	DMA_InitStructure.DMA_MemoryBaseAddr = cmar;
	DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralDST;
	DMA_InitStructure.DMA_BufferSize = cndtr;
	DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
	DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
	DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte;
	DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;
	DMA_InitStructure.DMA_Mode = DMA_Mode_Normal;
	DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;
	DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
	DMA_Init(DMA_CHx, &DMA_InitStructure);
}

/******************************************************************
 * @brief   ���ô��ڵ�DMA����ͨ��
 * @input  DMA_CHx:ѡ��DMA��ͨ��
 *         cmar��  Ҫ���͵�������ʼ��ַ
 *         cndtr�� ��������С
 *         cndtr:  ���ͻ��泤��
 * @return ��
******************************************************************/
void usart_DMA_Config_RX(DMA_Channel_TypeDef *DMA_CHx, uint32_t cpar, uint32_t cmar, uint16_t cndtr)
{
	DMA_InitTypeDef DMA_InitStructure;

	DMA_DeInit(DMA_CHx);
	DMA_InitStructure.DMA_PeripheralBaseAddr = cpar;
	DMA_InitStructure.DMA_MemoryBaseAddr = cmar;
	DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
	DMA_InitStructure.DMA_BufferSize = cndtr;
	DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
	DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
	DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Byte;
	DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_Byte;
	DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
	DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;
	DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
	DMA_Init(DMA_CHx, &DMA_InitStructure);

	DMA_Cmd(DMA_CHx, ENABLE);
}

/******************************************************************
 * @brief  ͨ��DMA��������
 * @input  data������
 *         length�����ݳ���
 * @return ��
******************************************************************/
void usart1_send_data(uint8_t *data, uint8_t len)
{
	DMA_ClearFlag(DMA1_FLAG_TC2);
	DMA_Cmd(DMA1_Channel2, DISABLE);
	memcpy(usart1_management.sendBuffer, data, len);
	DMA_Enable(DMA1_Channel2, len);
}

/******************************************************************
 * @brief  ����DMA����
 * @input  DMA_CHx��DMAͨ��
 *         CNDTR�����ݳ���
 * @return ��
******************************************************************/
void DMA_Enable(DMA_Channel_TypeDef *DMA_CHx, uint16_t CNDTR)
{
	DMA_CHx->CCR &= ~(1 << 0);
	DMA_CHx->CNDTR = CNDTR;
	DMA_CHx->CCR |= 1 << 0;
}

/******************************************************************
 * @brief  USART1���жϴ�������
 * @input  ��
 * @return ��
******************************************************************/
void USART1_IRQHandler(void)
{
	if (USART_GetFlagStatus(USART1, USART_FLAG_IDLE) != RESET)
	{
		DMA_Cmd(DMA1_Channel3, DISABLE);
		DMA_Enable(DMA1_Channel3, USART1_RECEIVE_BUFFER_SIZE);

		USART_ClearITPendingBit(USART1, USART_IT_IDLE);

        // 膨胀力传感器3数据处理：01 03 04 00 00 03 A7 BB 79
        if(usart1_management.receiveBuffer[0] == 0x01 && usart1_management.receiveBuffer[1] == 0x03 &&
		   usart1_management.receiveBuffer[2] == 0x04)
		{
			// 提取4字节long型数值：00 00 03 A7
			data_management.expansion_force3 = ((uint32_t)usart1_management.receiveBuffer[3] << 24) |
											   ((uint32_t)usart1_management.receiveBuffer[4] << 16) |
											   ((uint32_t)usart1_management.receiveBuffer[5] << 8) |
											   ((uint32_t)usart1_management.receiveBuffer[6]);
		}

        memset(usart1_management.receiveBuffer,0,sizeof(usart1_management.receiveBuffer));
	}
}

