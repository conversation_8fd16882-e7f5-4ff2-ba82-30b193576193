#ifndef __Hardware_H
#define __Hardware_H

#include "stm32f0xx.h"
#include "string.h"

#include "Delay.h"
#include "Usart1.h"
#include "Usart2.h"
// 暂时注释掉其他USART头文件，避免编译错误
// #include "Usart3.h"
// #include "Usart4.h"
// #include "Usart5.h"
// #include "Usart6.h"
// #include "Usart7.h"
// #include "Usart8.h"
#include "Can.h"
#include "Timer.h"

#include "Software.h"
#include "crc16.h"

void hardware_initializes(void);

// LED控制函数
void LED_blink(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin);
void LED1_blink(void);   // 膨胀力传感器3
void LED2_blink(void);   // CO传感器
void LED3_blink(void);   // H2传感器
void LED4_blink(void);   // 膨胀力传感器1
void LED5_blink(void);   // 膨胀力传感器2
void LED6_blink(void);   // 膨胀力传感器4
void LED7_blink(void);   // O2传感器
void LED8_blink(void);   // CO2传感器
void LED_CAN_blink(void); // CAN
void LED_turn(void);     // 兼容原有代码

#endif
