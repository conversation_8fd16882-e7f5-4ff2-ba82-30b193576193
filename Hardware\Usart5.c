//#include "Usart5.h"

//usart5_management_t usart5_management;

///******************************************************************
// * @brief  初始化串口5，配置中断及DMA进行数据接收和发送
// *         膨胀力传感器2：TX连接PB3、RX连接PB4
// * @input  无
// * @return 无
//******************************************************************/
//void usart5_initializes(void)
//{
//	USART_InitTypeDef USART_InitStructure;
//	GPIO_InitTypeDef  GPIO_InitStructure;
//	NVIC_InitTypeDef  NVIC_InitStructure;
//	
//	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART5, ENABLE);
//	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOB, ENABLE);

//	GPIO_PinAFConfig(GPIOB, GPIO_PinSource3, GPIO_AF_4);
//	GPIO_PinAFConfig(GPIOB, GPIO_PinSource4, GPIO_AF_4);

//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3 | GPIO_Pin_4;
//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
//	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
//	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
//	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
//	GPIO_Init(GPIOB, &GPIO_InitStructure);

//	USART_OverSampling8Cmd(USART5, ENABLE);
//	USART_InitStructure.USART_BaudRate = 9600;
//	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
//	USART_InitStructure.USART_StopBits = USART_StopBits_1;
//	USART_InitStructure.USART_Parity = USART_Parity_No;
//	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
//	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
//	USART_Init(USART5, &USART_InitStructure);
//    
//	/* usart 5 */
//	NVIC_InitStructure.NVIC_IRQChannel = USART3_8_IRQn;
//	NVIC_InitStructure.NVIC_IRQChannelPriority = 0;
//	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
//	NVIC_Init(&NVIC_InitStructure);

//	USART_ITConfig(USART5, USART_IT_IDLE, ENABLE);
//	USART_DMACmd(USART5, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);
//	USART_Cmd(USART5, ENABLE);
//	USART_ClearFlag(USART5, USART_FLAG_TC);
//	
//	usart_DMA_Config_TX(DMA1_Channel1, (uint32_t)&USART5->TDR, (uint32_t)usart5_management.sendBuffer, USART5_SEND_BUFFER_SIZE);
//	usart_DMA_Config_RX(DMA1_Channel2, (uint32_t)&USART5->RDR, (uint32_t)usart5_management.receiveBuffer, USART5_RECEIVE_BUFFER_SIZE);
//}

///******************************************************************
// * @brief  通过DMA发送数据
// * @input  data：数据
// *         length：数据长度
// * @return 无
//******************************************************************/
//void usart5_send_data(uint8_t *data, uint8_t len)
//{
//	DMA_ClearFlag(DMA1_FLAG_TC1);
//	DMA_Cmd(DMA1_Channel1, DISABLE);
//	memcpy(usart5_management.sendBuffer, data, len);
//	DMA_Enable(DMA1_Channel1, len);
//}

//// USART5中断处理在USART3_8_IRQHandler中统一处理
