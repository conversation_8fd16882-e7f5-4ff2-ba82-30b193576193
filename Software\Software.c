#include "Software.h"

data_management_t data_management;
// 暂时注释掉未使用的管理结构体
// usart3_management_t usart3_management;
// usart4_management_t usart4_management;
// usart5_management_t usart5_management;
// usart6_management_t usart6_management;
// usart7_management_t usart7_management;
// usart8_management_t usart8_management;

void software_initializes(void)
{
    memset(&usart1_management,0,sizeof(usart1_management));
	memset(&usart2_management,0,sizeof(usart2_management));
	// 暂时注释掉未使用的管理结构体初始化
	// memset(&usart3_management,0,sizeof(usart3_management));
	// memset(&usart4_management,0,sizeof(usart4_management));
	// memset(&usart5_management,0,sizeof(usart5_management));
	// memset(&usart6_management,0,sizeof(usart6_management));
	// memset(&usart7_management,0,sizeof(usart7_management));
	// memset(&usart8_management,0,sizeof(usart8_management));
	memset(&data_management,0,sizeof(data_management));
	memset(&timer2_management,0,sizeof(timer2_management));
}
