#include "Usart8.h"

usart8_management_t usart8_management;

/******************************************************************
 * @brief  初始化串口8，配置中断及DMA进行数据接收和发送
 *         CO2传感器：TX连接PC2、RX连接PC3
 * @input  无
 * @return 无
******************************************************************/
void usart8_initializes(void)
{
	USART_InitTypeDef USART_InitStructure;
	GPIO_InitTypeDef  GPIO_InitStructure;
	NVIC_InitTypeDef  NVIC_InitStructure;
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART8, ENABLE);
	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOC, ENABLE);

	GPIO_PinAFConfig(GPIOC, GPIO_PinSource2, GPIO_AF_1);
	GPIO_PinAFConfig(GPIOC, GPIO_PinSource3, GPIO_AF_1);

	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2 | GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOC, &GPIO_InitStructure);

	USART_OverSampling8Cmd(USART8, ENABLE);
	USART_InitStructure.USART_BaudRate = 9600;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART8, &USART_InitStructure);
    
	/* usart 8 */
	NVIC_InitStructure.NVIC_IRQChannel = USART3_8_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);

	USART_ITConfig(USART8, USART_IT_IDLE, ENABLE);
	USART_DMACmd(USART8, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);
	USART_Cmd(USART8, ENABLE);
	USART_ClearFlag(USART8, USART_FLAG_TC);
	
	usart_DMA_Config_TX(DMA2_Channel3, (uint32_t)&USART8->TDR, (uint32_t)usart8_management.sendBuffer, USART8_SEND_BUFFER_SIZE);
	usart_DMA_Config_RX(DMA2_Channel8, (uint32_t)&USART8->RDR, (uint32_t)usart8_management.receiveBuffer, USART8_RECEIVE_BUFFER_SIZE);
}

/******************************************************************
 * @brief  通过DMA发送数据
 * @input  data：数据
 *         length：数据长度
 * @return 无
******************************************************************/
void usart8_send_data(uint8_t *data, uint8_t len)
{
	DMA_ClearFlag(DMA2_FLAG_TC3);
	DMA_Cmd(DMA2_Channel3, DISABLE);
	memcpy(usart8_management.sendBuffer, data, len);
	DMA_Enable(DMA2_Channel3, len);
}

// USART8中断处理在USART3_8_IRQHandler中统一处理
