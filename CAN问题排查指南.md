# CAN总线问题排查指南

## 问题现象
- LED灯能正常运行（说明程序正常执行）
- CAN总线接收不到数据

## 可能原因分析

### 1. 硬件连接问题 ⚠️
**最常见的问题**

#### CAN收发器检查
- 确认是否使用了CAN收发器芯片（如TJA1050、SN65HVD230等）
- STM32的CAN控制器只能输出3.3V逻辑电平，无法直接驱动CAN总线
- CAN总线需要差分信号（CAN_H和CAN_L）

#### 引脚连接检查
```
STM32F091 -> CAN收发器 -> CAN总线
PA11 (CAN_RX) -> CTX -> CAN_H/CAN_L
PA12 (CAN_TX) -> CRX -> CAN_H/CAN_L
```

#### 终端电阻检查
- CAN总线两端需要120Ω终端电阻
- 用万用表测量CAN_H和CAN_L之间应该是60Ω（两个120Ω并联）

### 2. CAN波特率配置问题

#### 当前配置分析
```c
// 当前配置：APB1时钟8M，预分频2，CAN时钟4M，目标500K
CAN_InitStructure.CAN_Prescaler = 2;
CAN_InitStructure.CAN_SJW = CAN_SJW_1tq;
CAN_InitStructure.CAN_BS1 = CAN_BS1_3tq;
CAN_InitStructure.CAN_BS2 = CAN_BS2_4tq;
```

#### 波特率计算
```
波特率 = CAN时钟 / (预分频 × (1 + BS1 + BS2))
      = 4M / (1 × (1 + 3 + 4))
      = 4M / 8
      = 500kbps
```

#### 常用波特率配置
```c
// 250kbps配置
CAN_InitStructure.CAN_Prescaler = 4;
CAN_InitStructure.CAN_SJW = CAN_SJW_1tq;
CAN_InitStructure.CAN_BS1 = CAN_BS1_3tq;
CAN_InitStructure.CAN_BS2 = CAN_BS2_4tq;

// 125kbps配置
CAN_InitStructure.CAN_Prescaler = 8;
CAN_InitStructure.CAN_SJW = CAN_SJW_1tq;
CAN_InitStructure.CAN_BS1 = CAN_BS1_3tq;
CAN_InitStructure.CAN_BS2 = CAN_BS2_4tq;
```

### 3. CAN网络配置问题

#### 确认网络中的其他设备
- 确认CAN网络中是否有其他正常工作的节点
- 确认接收设备的波特率设置
- 确认接收设备的ID过滤设置

### 4. 软件配置问题

#### CAN模式检查
当前使用正常模式，这是正确的：
```c
CAN_InitStructure.CAN_Mode = CAN_Mode_Normal;
```

#### 自动重传设置
```c
CAN_InitStructure.CAN_NART = ENABLE;  // 启用自动重传
```

## 排查步骤

### 第1步：硬件基础检查
1. **电源检查**
   - 确认CAN收发器供电正常（通常5V或3.3V）
   - 确认STM32供电正常

2. **连接检查**
   - 用万用表检查PA11、PA12与CAN收发器的连接
   - 检查CAN_H、CAN_L线路连接
   - 检查终端电阻

### 第2步：使用示波器/逻辑分析仪
1. **检查PA12（CAN_TX）**
   - 应该能看到数据发送时的波形变化
   - 如果没有波形，说明软件问题

2. **检查CAN_H、CAN_L**
   - 应该能看到差分信号
   - 如果PA12有信号但CAN_H/CAN_L没有，说明收发器问题

### 第3步：软件调试
1. **添加调试代码**
```c
// 在CAN_SendData函数中添加状态检查
uint8_t can_status = CAN_TransmitStatus(CAN, mailbox);
// 通过LED或串口输出状态
```

2. **检查CAN状态寄存器**
```c
uint32_t can_esr = CAN->ESR;  // 错误状态寄存器
uint32_t can_msr = CAN->MSR;  // 主状态寄存器
```

### 第4步：回环测试
临时修改为回环模式进行自测试：
```c
CAN_InitStructure.CAN_Mode = CAN_Mode_LoopBack;
```

## 快速修复建议

### 1. 修改CAN配置为更常用的波特率
```c
// 修改为250kbps（更稳定）
CAN_InitStructure.CAN_Prescaler = 4;
```

### 2. 添加CAN状态检查
在main.c中添加CAN状态检查：
```c
// 在发送CAN数据前检查CAN状态
if((CAN->MSR & CAN_MSR_INAK) == 0) {
    // CAN正常工作
    CAN_SendData(0x20A, can_data, 8);
} else {
    // CAN初始化失败，LED指示
    LED_CAN_blink();
}
```

### 3. 简化测试
先发送一个简单的测试帧：
```c
uint8_t test_data[8] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
CAN_SendData(0x123, test_data, 8);
```

## 常见解决方案

### 方案1：检查硬件（最重要）
- 确认使用了CAN收发器
- 检查终端电阻
- 检查线路连接

### 方案2：修改波特率
- 尝试250kbps或125kbps
- 确认与接收设备一致

### 方案3：使用CAN分析仪
- 使用专业CAN分析仪检查总线状态
- 确认是否有数据发送

### 方案4：回环测试
- 先用回环模式验证软件功能
- 再切换到正常模式测试硬件

## 调试工具推荐

1. **万用表**：检查电源、连接、终端电阻
2. **示波器**：检查信号波形
3. **CAN分析仪**：专业CAN总线调试工具
4. **逻辑分析仪**：检查数字信号时序

## 预期结果

修复后应该能够：
- 在CAN分析仪上看到发送的数据帧
- 接收设备能正常接收数据
- CAN状态寄存器显示正常

如果问题仍然存在，建议按照上述步骤逐一排查，重点检查硬件连接和CAN收发器配置。
