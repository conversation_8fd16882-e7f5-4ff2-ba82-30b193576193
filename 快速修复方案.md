# 快速编译修复方案

## 问题分析
当前编译错误主要由以下原因造成：
1. STM32F091的DMA通道限制（只有DMA1的7个通道）
2. 复杂的多USART配置导致资源冲突
3. C90标准兼容性问题

## 快速修复方案

### 方案1：最小可行版本（推荐）
保留原有的USART1和USART2，暂时禁用USART3-8，确保项目能够编译和运行。

#### 修改步骤：
1. 在Hardware.c中注释掉USART3-8的初始化
2. 在main.c中注释掉USART3-8的调用
3. 保持原有的基本功能

### 方案2：逐步添加USART
先确保USART1-2正常工作，然后逐个添加USART3-8。

### 方案3：使用软件UART
对于部分传感器，使用GPIO模拟UART通信。

## 立即可执行的修复

### 1. 修改Hardware.c
```c
void hardware_initializes(void)
{
    GPIO_InitTypeDef  GPIO_InitStructure;
    
    delay_init();
    
    // 只初始化USART1和USART2
    usart1_initializes();  // 膨胀力传感器3
    usart2_initializes();  // CO传感器
    
    // 暂时注释掉其他USART
    // usart3_initializes();  // H2传感器
    // usart4_initializes();  // 膨胀力传感器1
    // usart5_initializes();  // 膨胀力传感器2
    // usart6_initializes();  // 膨胀力传感器4
    // usart7_initializes();  // O2传感器
    // usart8_initializes();  // CO2传感器
    
    can_initializes();
    timer_initializes();
    
    // 配置基本LED
    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOA | RCC_AHBPeriph_GPIOB, ENABLE);
    
    // LED1（膨胀力传感器3）：PB9
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(GPIOB, &GPIO_InitStructure);
    GPIO_SetBits(GPIOB, GPIO_Pin_9);
    
    // LED2（CO传感器）：PA4
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    GPIO_SetBits(GPIOA, GPIO_Pin_4);
    
    // LED CAN：PB12
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;
    GPIO_Init(GPIOB, &GPIO_InitStructure);
    GPIO_SetBits(GPIOB, GPIO_Pin_12);
}
```

### 2. 修改main.c
```c
int main(void)
{	
    hardware_initializes();
    software_initializes();
    
    while(1)
    {
        if(timer2_management.flag_100ms)
        {
            timer2_management.flag_100ms = 0;
        }
        
        if(timer2_management.flag_900ms)
        {
            timer2_management.flag_900ms = 0;
            
            // 只发送基本传感器查询
            usart2_send_data(co_query_cmd, 4);
            LED2_blink();
            
            usart1_send_data(expansion_query_cmd, 8);
            LED1_blink();
        }
        
        if(timer2_management.flag_1s)
        {
            uint8_t can_data[8];
            
            timer2_management.flag_1s = 0;
            
            // 发送基本数据
            can_data[0] = (data_management.co_concentration >> 8) & 0xFF;
            can_data[1] = data_management.co_concentration & 0xFF;
            can_data[2] = 0; // H2浓度暂时为0
            can_data[3] = 0;
            can_data[4] = 0; // O2浓度暂时为0
            can_data[5] = 0;
            can_data[6] = 0; // CO2浓度暂时为0
            can_data[7] = 0;
            CAN_SendData(0x20A, can_data, 8);
            
            // 发送膨胀力数据
            can_data[0] = (data_management.expansion_force3 >> 24) & 0xFF;
            can_data[1] = (data_management.expansion_force3 >> 16) & 0xFF;
            can_data[2] = (data_management.expansion_force3 >> 8) & 0xFF;
            can_data[3] = data_management.expansion_force3 & 0xFF;
            can_data[4] = 0; // 其他膨胀力暂时为0
            can_data[5] = 0;
            can_data[6] = 0;
            can_data[7] = 0;
            CAN_SendData(0x20B, can_data, 8);
            
            LED_CAN_blink();
        }
    }
}
```

### 3. 修改Hardware.h
```c
// 暂时注释掉未使用的头文件
#include "Delay.h"
#include "Usart1.h"
#include "Usart2.h"
// #include "Usart3.h"
// #include "Usart4.h"
// #include "Usart5.h"
// #include "Usart6.h"
// #include "Usart7.h"
// #include "Usart8.h"
#include "Can.h"
#include "Timer.h"
```

## 编译验证步骤

1. **第一步**：应用上述修改，确保项目能够编译通过
2. **第二步**：在硬件上测试基本功能（USART1、USART2、CAN）
3. **第三步**：逐个添加其他USART接口
4. **第四步**：优化和完善功能

## 预期结果

应用此修复方案后，项目应该能够：
- ✅ 编译通过，无语法错误
- ✅ 支持基本的传感器通信（CO传感器、膨胀力传感器3）
- ✅ 支持CAN数据发送
- ✅ 支持LED指示
- ⚠️ 暂时不支持H2、O2、CO2传感器和其他膨胀力传感器

## 后续扩展计划

1. **阶段1**：验证基本功能正常
2. **阶段2**：添加USART3（H2传感器），使用简单中断模式
3. **阶段3**：添加USART4-6（其他膨胀力传感器）
4. **阶段4**：添加USART7-8（O2、CO2传感器）
5. **阶段5**：优化性能和稳定性

这种渐进式的方法可以确保每个阶段都有可工作的版本，便于调试和验证。
