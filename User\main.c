#include "Hardware.h"

// 传感器查询指令
uint8_t co_query_cmd[4] = {0x11, 0x01, 0x01, 0xED};                           // CO传感器查询指令
uint8_t h2_query_cmd[9] = {0xFF, 0x01, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79}; // H2传感器查询指令
uint8_t expansion_query_cmd[8] = {0x01, 0x03, 0x03, 0x00, 0x00, 0x02, 0xC4, 0x4F}; // 膨胀力传感器查询指令
uint8_t co2_concentration_cmd[8] = {0xFF, 0x03, 0x60, 0x01, 0x00, 0x02, 0x9E, 0x15}; // CO2浓度查询指令
uint8_t co2_decimal_cmd[8] = {0xFF, 0x03, 0x20, 0x31, 0x00, 0x01, 0xCB, 0xDB};      // CO2小数点查询指令

// 兼容原有代码
uint8_t read_data_command[8] = {0xff, 0x03, 0x60, 0x01, 0x00, 0x01, 0xde, 0x14};

int main(void)
{	
	hardware_initializes();
	software_initializes();
	
	while(1)
	{
		if(timer2_management.flag_100ms)
		{
			timer2_management.flag_100ms = 0;
			// 可以在这里添加100ms周期性任务
		}
		
		// 每隔900ms发送传感器查询命令
		if(timer2_management.flag_900ms)
		{
			timer2_management.flag_900ms = 0;

			// 只发送基本传感器查询指令
			usart2_send_data(co_query_cmd, 4);                    // CO传感器查询
			LED2_blink();

			usart1_send_data(expansion_query_cmd, 8);             // 膨胀力传感器3查询
			LED1_blink();

			// 其他传感器暂时注释，避免编译错误
			// usart3_send_data(h2_query_cmd, 9);                // H2传感器查询
			// usart4_send_data(expansion_query_cmd, 8);          // 膨胀力传感器1查询
			// usart5_send_data(expansion_query_cmd, 8);          // 膨胀力传感器2查询
			// usart6_send_data(expansion_query_cmd, 8);          // 膨胀力传感器4查询
			// usart7_send_data(h2_query_cmd, 9);                // O2传感器查询
			// usart8_send_data(co2_concentration_cmd, 8);       // CO2浓度查询
		}
		
		// 每隔一秒通过CAN发送数据
		if(timer2_management.flag_1s)
		{
			uint8_t can_data[8];  // 在C90标准中，变量声明必须在代码块开始处

			timer2_management.flag_1s = 0;

			// 按要求的顺序打包数据：CO浓度、H2浓度、O2浓度、CO2浓度、膨胀力1、膨胀力2、膨胀力3、膨胀力4
			
			// 第一帧：气体浓度数据（暂时只有CO数据有效）
			can_data[0] = (data_management.co_concentration >> 8) & 0xFF;
			can_data[1] = data_management.co_concentration & 0xFF;
			can_data[2] = 0; // H2浓度暂时为0
			can_data[3] = 0;
			can_data[4] = 0; // O2浓度暂时为0
			can_data[5] = 0;
			can_data[6] = 0; // CO2浓度暂时为0
			can_data[7] = 0;
			CAN_SendData(0x20A, can_data, 8);

			// 第二帧：膨胀力数据（暂时只有膨胀力3有效）
			can_data[0] = (data_management.expansion_force3 >> 24) & 0xFF;
			can_data[1] = (data_management.expansion_force3 >> 16) & 0xFF;
			can_data[2] = (data_management.expansion_force3 >> 8) & 0xFF;
			can_data[3] = data_management.expansion_force3 & 0xFF;
			can_data[4] = 0; // 其他膨胀力暂时为0
			can_data[5] = 0;
			can_data[6] = 0;
			can_data[7] = 0;
			CAN_SendData(0x20B, can_data, 8);
			
			LED_CAN_blink();
		}
	}
}
