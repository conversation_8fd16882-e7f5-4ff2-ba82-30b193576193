#include "Hardware.h"

void hardware_initializes(void)
{
	GPIO_InitTypeDef  GPIO_InitStructure;

	delay_init();

	// 初始化基本USART（先确保这两个能正常工作）
	usart1_initializes();  // 膨胀力传感器3
	usart2_initializes();  // CO传感器

	// 暂时注释掉其他USART，避免资源冲突
	// usart3_initializes();  // H2传感器
	// usart4_initializes();  // 膨胀力传感器1
	// usart5_initializes();  // 膨胀力传感器2
	// usart6_initializes();  // 膨胀力传感器4
	// usart7_initializes();  // O2传感器
	// usart8_initializes();  // CO2传感器

	can_initializes();
	timer_initializes();

	// 配置基本LED GPIO
	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOA | RCC_AHBPeriph_GPIOB, ENABLE);

	// LED1（膨胀力传感器3）：PB9
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	GPIO_SetBits(GPIOB, GPIO_Pin_9);  // LED高电平熄灭

	// LED2（CO传感器）：PA4
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	GPIO_SetBits(GPIOA, GPIO_Pin_4);

	// LED CAN：PB12
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	GPIO_SetBits(GPIOB, GPIO_Pin_12);

	// 其他LED暂时注释，避免GPIO冲突
	// LED3（H2传感器）：PB0
	// LED4（膨胀力传感器1）：PA15
	// LED5（膨胀力传感器2）：PD2
	// LED6（膨胀力传感器4）：PC15-OSC32_OUT
	// LED7（O2传感器）：PC8
	// LED8（CO2传感器）：PA0
}

// LED闪烁控制函数（LED高电平熄灭，低电平点亮）
void LED_blink(GPIO_TypeDef* GPIOx, uint16_t GPIO_Pin)
{
	GPIO_ResetBits(GPIOx, GPIO_Pin);  // 点亮LED
	delay_ms(50);                     // 延时50ms
	GPIO_SetBits(GPIOx, GPIO_Pin);    // 熄灭LED
}

// 各个传感器LED闪烁函数
void LED1_blink(void) { LED_blink(GPIOB, GPIO_Pin_9); }   // 膨胀力传感器3
void LED2_blink(void) { LED_blink(GPIOA, GPIO_Pin_4); }   // CO传感器
void LED3_blink(void) { LED_blink(GPIOB, GPIO_Pin_0); }   // H2传感器
void LED4_blink(void) { LED_blink(GPIOA, GPIO_Pin_15); }  // 膨胀力传感器1
void LED5_blink(void) { LED_blink(GPIOD, GPIO_Pin_2); }   // 膨胀力传感器2
void LED6_blink(void) { LED_blink(GPIOC, GPIO_Pin_15); }  // 膨胀力传感器4
void LED7_blink(void) { LED_blink(GPIOC, GPIO_Pin_8); }   // O2传感器
void LED8_blink(void) { LED_blink(GPIOA, GPIO_Pin_0); }   // CO2传感器
void LED_CAN_blink(void) { LED_blink(GPIOB, GPIO_Pin_12); } // CAN

// 兼容原有代码的LED_turn函数
void LED_turn(void)
{
	LED_CAN_blink();
}
