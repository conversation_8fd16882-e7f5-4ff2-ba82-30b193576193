#include "Usart6.h"

usart6_management_t usart6_management;

/******************************************************************
 * @brief  初始化串口6，配置中断及DMA进行数据接收和发送
 *         膨胀力传感器4：TX连接PC0、RX连接PC1
 * @input  无
 * @return 无
******************************************************************/
void usart6_initializes(void)
{
	USART_InitTypeDef USART_InitStructure;
	GPIO_InitTypeDef  GPIO_InitStructure;
	NVIC_InitTypeDef  NVIC_InitStructure;
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART6, ENABLE);
	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOC, ENABLE);

	GPIO_PinAFConfig(GPIOC, GPIO_PinSource0, GPIO_AF_2);
	GPIO_PinAFConfig(GPIOC, GPIO_PinSource1, GPIO_AF_2);

	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOC, &GPIO_InitStructure);

	USART_OverSampling8Cmd(USART6, ENABLE);
	USART_InitStructure.USART_BaudRate = 9600;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART6, &USART_InitStructure);
    
	/* usart 6 */
	NVIC_InitStructure.NVIC_IRQChannel = USART3_8_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);

	USART_ITConfig(USART6, USART_IT_IDLE, ENABLE);
	USART_DMACmd(USART6, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);
	USART_Cmd(USART6, ENABLE);
	USART_ClearFlag(USART6, USART_FLAG_TC);
	
	usart_DMA_Config_TX(DMA2_Channel6, (uint32_t)&USART6->TDR, (uint32_t)usart6_management.sendBuffer, USART6_SEND_BUFFER_SIZE);
	usart_DMA_Config_RX(DMA2_Channel7, (uint32_t)&USART6->RDR, (uint32_t)usart6_management.receiveBuffer, USART6_RECEIVE_BUFFER_SIZE);
}

/******************************************************************
 * @brief  通过DMA发送数据
 * @input  data：数据
 *         length：数据长度
 * @return 无
******************************************************************/
void usart6_send_data(uint8_t *data, uint8_t len)
{
	DMA_ClearFlag(DMA2_FLAG_TC6);
	DMA_Cmd(DMA2_Channel6, DISABLE);
	memcpy(usart6_management.sendBuffer, data, len);
	DMA_Enable(DMA2_Channel6, len);
}

// USART6中断处理在USART3_8_IRQHandler中统一处理
