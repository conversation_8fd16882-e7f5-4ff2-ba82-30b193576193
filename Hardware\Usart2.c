#include "Usart2.h"

usart2_management_t usart2_management;


/******************************************************************
 * @brief  ��ʼ������2�������жϼ�DMA������ݽ��պͽ���
 *         ����DMA���ͣ���������Զ���������
 * @input  ��
 * @return ��
******************************************************************/
void usart2_initializes(void)
{
	USART_InitTypeDef USART_InitStructure;
	GPIO_InitTypeDef  GPIO_InitStructure;
	NVIC_InitTypeDef  NVIC_InitStructure;
	
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOA, ENABLE);

	GPIO_PinAFConfig(GPIOA, GPIO_PinSource2, GPIO_AF_1);
	GPIO_PinAFConfig(GPIOA, GPIO_PinSource3, GPIO_AF_1);

	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2 | GPIO_Pin_3;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	USART_OverSampling8Cmd(USART2, ENABLE);
	USART_InitStructure.USART_BaudRate = 9600;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART2, &USART_InitStructure);
    
	/* usart 2 */
	NVIC_InitStructure.NVIC_IRQChannel = USART2_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);

	USART_ITConfig(USART2, USART_IT_IDLE, ENABLE);
	USART_DMACmd(USART2, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);
	USART_Cmd(USART2, ENABLE);
	USART_ClearFlag(USART2, USART_FLAG_TC);
	
	usart_DMA_Config_TX(DMA1_Channel4, (uint32_t)&USART2->TDR, (uint32_t)usart2_management.sendBuffer, USART2_SEND_BUFFER_SIZE);
	usart_DMA_Config_RX(DMA1_Channel5, (uint32_t)&USART2->RDR, (uint32_t)usart2_management.receiveBuffer, USART2_RECEIVE_BUFFER_SIZE);
}

/******************************************************************
 * @brief  ͨ��DMA��������
 * @input  data������
 *         length�����ݳ���
 * @return ��
******************************************************************/
void usart2_send_data(uint8_t *data, uint8_t len)
{
	DMA_ClearFlag(DMA1_FLAG_TC4);
	DMA_Cmd(DMA1_Channel4, DISABLE);
	memcpy(usart2_management.sendBuffer, data, len);
	DMA_Enable(DMA1_Channel4, len);
}

/******************************************************************
 * @brief  USART2���жϴ�������
 * @input  ��
 * @return ��
******************************************************************/
void USART2_IRQHandler(void)
{
	uint16_t temp;
	
	if (USART_GetFlagStatus(USART2, USART_FLAG_IDLE) != RESET)
	{
		DMA_Cmd(DMA1_Channel5, DISABLE);
		DMA_Enable(DMA1_Channel5, USART2_RECEIVE_BUFFER_SIZE);

		USART_ClearITPendingBit(USART2, USART_IT_IDLE);

        // CO传感器数据处理：16 05 01 DF1-DF2 DF3-DF4 [CS]
        if(usart2_management.receiveBuffer[0] == 0x16 && usart2_management.receiveBuffer[1] == 0x05 &&
		   usart2_management.receiveBuffer[2] == 0x01)
		{
			// CO浓度值=（DF1×256+DF2)/5
			uint16_t df1_df2 = (usart2_management.receiveBuffer[3] << 8) | usart2_management.receiveBuffer[4];
			data_management.co_concentration = df1_df2 / 5;

			// 兼容原有代码格式
			data_management.concentration[0] = usart2_management.receiveBuffer[3];
			data_management.concentration[1] = usart2_management.receiveBuffer[4];
		}

        memset(usart2_management.receiveBuffer,0,sizeof(usart2_management.receiveBuffer));
	}
}

