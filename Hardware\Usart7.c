//#include "Usart7.h"

//usart7_management_t usart7_management;

///******************************************************************
// * @brief  初始化串口7，配置中断及DMA进行数据接收和发送
// *         O2传感器：TX连接PC6、RX连接PC7
// * @input  无
// * @return 无
//******************************************************************/
//void usart7_initializes(void)
//{
//	USART_InitTypeDef USART_InitStructure;
//	GPIO_InitTypeDef  GPIO_InitStructure;
//	NVIC_InitTypeDef  NVIC_InitStructure;
//	
//	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART7, ENABLE);
//	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOC, ENABLE);

//	GPIO_PinAFConfig(GPIOC, GPIO_PinSource6, GPIO_AF_1);
//	GPIO_PinAFConfig(GPIOC, GPIO_PinSource7, GPIO_AF_1);

//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6 | GPIO_Pin_7;
//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
//	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
//	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
//	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
//	GPIO_Init(GPIOC, &GPIO_InitStructure);

//	USART_OverSampling8Cmd(USART7, ENABLE);
//	USART_InitStructure.USART_BaudRate = 9600;
//	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
//	USART_InitStructure.USART_StopBits = USART_StopBits_1;
//	USART_InitStructure.USART_Parity = USART_Parity_No;
//	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
//	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
//	USART_Init(USART7, &USART_InitStructure);
//    
//	/* usart 7 */
//	NVIC_InitStructure.NVIC_IRQChannel = USART3_8_IRQn;
//	NVIC_InitStructure.NVIC_IRQChannelPriority = 0;
//	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
//	NVIC_Init(&NVIC_InitStructure);

//	USART_ITConfig(USART7, USART_IT_IDLE, ENABLE);
//	USART_DMACmd(USART7, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);
//	USART_Cmd(USART7, ENABLE);
//	USART_ClearFlag(USART7, USART_FLAG_TC);
//	
//	usart_DMA_Config_TX(DMA1_Channel4, (uint32_t)&USART7->TDR, (uint32_t)usart7_management.sendBuffer, USART7_SEND_BUFFER_SIZE);
//	usart_DMA_Config_RX(DMA1_Channel5, (uint32_t)&USART7->RDR, (uint32_t)usart7_management.receiveBuffer, USART7_RECEIVE_BUFFER_SIZE);
//}

///******************************************************************
// * @brief  通过DMA发送数据
// * @input  data：数据
// *         length：数据长度
// * @return 无
//******************************************************************/
//void usart7_send_data(uint8_t *data, uint8_t len)
//{
//	DMA_ClearFlag(DMA1_FLAG_TC4);
//	DMA_Cmd(DMA1_Channel4, DISABLE);
//	memcpy(usart7_management.sendBuffer, data, len);
//	DMA_Enable(DMA1_Channel4, len);
//}

//// USART7中断处理在USART3_8_IRQHandler中统一处理
