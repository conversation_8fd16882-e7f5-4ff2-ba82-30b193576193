Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    system_stm32f0xx.o(i.SystemCoreClockUpdate) refers to aeabi_sdiv.o(.text) for __aeabi_uidivmod
    system_stm32f0xx.o(i.SystemCoreClockUpdate) refers to system_stm32f0xx.o(.data) for SystemCoreClock
    system_stm32f0xx.o(i.SystemInit) refers to system_stm32f0xx.o(i.SetSysClock) for SetSysClock
    startup_stm32f091.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(RESET) refers to startup_stm32f091.o(STACK) for __initial_sp
    startup_stm32f091.o(RESET) refers to startup_stm32f091.o(.text) for Reset_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f091.o(RESET) refers to timer.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f091.o(RESET) refers to usart1.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f091.o(RESET) refers to usart2.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f091.o(RESET) refers to can.o(i.CEC_CAN_IRQHandler) for CEC_CAN_IRQHandler
    startup_stm32f091.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(.text) refers to startup_stm32f091.o(STACK) for __initial_sp
    startup_stm32f091.o(.text) refers to system_stm32f0xx.o(i.SystemInit) for SystemInit
    startup_stm32f091.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f091.o(.text) refers to startup_stm32f091.o(HEAP) for Heap_Mem
    stm32f0xx_can.o(i.CAN_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f0xx_can.o(i.CAN_GetITStatus) refers to stm32f0xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f0xx_gpio.o(i.GPIO_DeInit) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphResetCmd) for RCC_AHBPeriphResetCmd
    stm32f0xx_rcc.o(i.RCC_GetClocksFreq) refers to aeabi_sdiv.o(.text) for __aeabi_uidivmod
    stm32f0xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f0xx_rcc.o(.data) for APBAHBPrescTable
    stm32f0xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f0xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f0xx_tim.o(i.TIM_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f0xx_tim.o(i.TIM_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f0xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f0xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f0xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f0xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f0xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f0xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f0xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f0xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f0xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f0xx_usart.o(i.USART_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f0xx_usart.o(i.USART_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f0xx_usart.o(i.USART_Init) refers to stm32f0xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f0xx_usart.o(i.USART_Init) refers to aeabi_sdiv.o(.text) for __aeabi_uidivmod
    main.o(i.main) refers to hardware.o(i.hardware_initializes) for hardware_initializes
    main.o(i.main) refers to software.o(i.software_initializes) for software_initializes
    main.o(i.main) refers to usart2.o(i.usart2_send_data) for usart2_send_data
    main.o(i.main) refers to can.o(i.CAN_SendData) for CAN_SendData
    main.o(i.main) refers to hardware.o(i.LED_turn) for LED_turn
    main.o(i.main) refers to timer.o(.data) for timer2_management
    main.o(i.main) refers to main.o(.data) for read_data_command
    main.o(i.main) refers to software.o(.data) for data_management
    hardware.o(i.LED_turn) refers to stm32f0xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    hardware.o(i.LED_turn) refers to stm32f0xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    hardware.o(i.LED_turn) refers to stm32f0xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    hardware.o(i.hardware_initializes) refers to delay.o(i.delay_init) for delay_init
    hardware.o(i.hardware_initializes) refers to usart1.o(i.usart1_initializes) for usart1_initializes
    hardware.o(i.hardware_initializes) refers to usart2.o(i.usart2_initializes) for usart2_initializes
    hardware.o(i.hardware_initializes) refers to can.o(i.can_initializes) for can_initializes
    hardware.o(i.hardware_initializes) refers to timer.o(i.timer_initializes) for timer_initializes
    hardware.o(i.hardware_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    hardware.o(i.hardware_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    hardware.o(i.hardware_initializes) refers to stm32f0xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    delay.o(i.delay_init) refers to stm32f0xx_misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    usart1.o(i.USART1_IRQHandler) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(i.USART1_IRQHandler) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart1.o(i.USART1_IRQHandler) refers to stm32f0xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart1.o(i.USART1_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(.bss) for usart1_management
    usart1.o(i.USART1_IRQHandler) refers to software.o(.data) for data_management
    usart1.o(i.usart1_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart1.o(i.usart1_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart1.o(i.usart1_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart1.o(i.usart1_initializes) refers to usart1.o(i.usart_DMA_Config_TX) for usart_DMA_Config_TX
    usart1.o(i.usart1_initializes) refers to usart1.o(i.usart_DMA_Config_RX) for usart_DMA_Config_RX
    usart1.o(i.usart1_initializes) refers to usart1.o(.bss) for usart1_management
    usart1.o(i.usart1_send_data) refers to stm32f0xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    usart1.o(i.usart1_send_data) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart1.o(i.usart1_send_data) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    usart1.o(i.usart1_send_data) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart1.o(i.usart1_send_data) refers to usart1.o(.bss) for usart1_management
    usart1.o(i.usart_DMA_Config_RX) refers to stm32f0xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    usart1.o(i.usart_DMA_Config_RX) refers to stm32f0xx_dma.o(i.DMA_Init) for DMA_Init
    usart1.o(i.usart_DMA_Config_RX) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart1.o(i.usart_DMA_Config_TX) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart1.o(i.usart_DMA_Config_TX) refers to stm32f0xx_dma.o(i.DMA_Init) for DMA_Init
    usart2.o(i.USART2_IRQHandler) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart2.o(i.USART2_IRQHandler) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart2.o(i.USART2_IRQHandler) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart2.o(i.USART2_IRQHandler) refers to stm32f0xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart2.o(i.USART2_IRQHandler) refers to crc16.o(i.Crc16) for Crc16
    usart2.o(i.USART2_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart2.o(i.USART2_IRQHandler) refers to usart2.o(.bss) for usart2_management
    usart2.o(i.USART2_IRQHandler) refers to software.o(.data) for data_management
    usart2.o(i.usart2_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart2.o(i.usart2_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart2.o(i.usart2_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart2.o(i.usart2_initializes) refers to usart1.o(i.usart_DMA_Config_TX) for usart_DMA_Config_TX
    usart2.o(i.usart2_initializes) refers to usart1.o(i.usart_DMA_Config_RX) for usart_DMA_Config_RX
    usart2.o(i.usart2_initializes) refers to usart2.o(.bss) for usart2_management
    usart2.o(i.usart2_send_data) refers to stm32f0xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    usart2.o(i.usart2_send_data) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart2.o(i.usart2_send_data) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    usart2.o(i.usart2_send_data) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart2.o(i.usart2_send_data) refers to usart2.o(.bss) for usart2_management
    can.o(i.CAN1_Receive) refers to stm32f0xx_can.o(i.CAN_MessagePending) for CAN_MessagePending
    can.o(i.CAN1_Receive) refers to stm32f0xx_can.o(i.CAN_Receive) for CAN_Receive
    can.o(i.CAN1_Receive) refers to can.o(.data) for j
    can.o(i.CAN1_Receive) refers to can.o(.bss) for buf1
    can.o(i.CAN_SendData) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    can.o(i.CAN_SendData) refers to stm32f0xx_can.o(i.CAN_Transmit) for CAN_Transmit
    can.o(i.CAN_SendData) refers to stm32f0xx_can.o(i.CAN_TransmitStatus) for CAN_TransmitStatus
    can.o(i.CEC_CAN_IRQHandler) refers to stm32f0xx_can.o(i.CAN_GetITStatus) for CAN_GetITStatus
    can.o(i.CEC_CAN_IRQHandler) refers to stm32f0xx_can.o(i.CAN_ClearITPendingBit) for CAN_ClearITPendingBit
    can.o(i.CEC_CAN_IRQHandler) refers to can.o(i.CAN1_Receive) for CAN1_Receive
    can.o(i.CEC_CAN_IRQHandler) refers to can.o(.data) for j
    can.o(i.can_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    can.o(i.can_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    can.o(i.can_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    can.o(i.can_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    can.o(i.can_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_DeInit) for CAN_DeInit
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_StructInit) for CAN_StructInit
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_Init) for CAN_Init
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_FilterInit) for CAN_FilterInit
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_ClearFlag) for CAN_ClearFlag
    timer.o(i.TIM2_IRQHandler) refers to stm32f0xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    timer.o(i.TIM2_IRQHandler) refers to stm32f0xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer.o(i.TIM2_IRQHandler) refers to timer.o(.data) for timer2_management
    timer.o(i.timer_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.timer_initializes) refers to stm32f0xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.timer_initializes) refers to stm32f0xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.timer_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.timer_initializes) refers to stm32f0xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    software.o(i.software_initializes) refers to rt_memclr.o(.text) for __aeabi_memclr
    software.o(i.software_initializes) refers to usart1.o(.bss) for usart1_management
    software.o(i.software_initializes) refers to usart2.o(.bss) for usart2_management
    software.o(i.software_initializes) refers to software.o(.data) for data_management
    software.o(i.software_initializes) refers to timer.o(.data) for timer2_management
    crc16.o(i.Crc16) refers to crc16.o(.constdata) for u16CrcTalbeAbs
    rt_memcpy.o(.text) refers to rt_memcpy.o(.emb_text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f091.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f0xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f0xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f0xx.o(i.SystemCoreClockUpdate), (176 bytes).
    Removing system_stm32f0xx.o(.data), (20 bytes).
    Removing stm32f0xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_can.o(i.CAN_CancelTransmit), (54 bytes).
    Removing stm32f0xx_can.o(i.CAN_DBGFreeze), (28 bytes).
    Removing stm32f0xx_can.o(i.CAN_FIFORelease), (24 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetFlagStatus), (146 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (16 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetLastErrorCode), (14 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f0xx_can.o(i.CAN_ITConfig), (20 bytes).
    Removing stm32f0xx_can.o(i.CAN_OperatingModeRequest), (172 bytes).
    Removing stm32f0xx_can.o(i.CAN_SlaveStartBank), (56 bytes).
    Removing stm32f0xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f0xx_can.o(i.CAN_TTComModeCmd), (128 bytes).
    Removing stm32f0xx_can.o(i.CAN_WakeUp), (52 bytes).
    Removing stm32f0xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_dma.o(i.DMA_ClearITPendingBit), (32 bytes).
    Removing stm32f0xx_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f0xx_dma.o(i.DMA_GetFlagStatus), (52 bytes).
    Removing stm32f0xx_dma.o(i.DMA_GetITStatus), (52 bytes).
    Removing stm32f0xx_dma.o(i.DMA_ITConfig), (20 bytes).
    Removing stm32f0xx_dma.o(i.DMA_RemapConfig), (52 bytes).
    Removing stm32f0xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f0xx_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f0xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_DeInit), (32 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_GetFlagStatus), (28 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_GetITStatus), (28 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_Init), (136 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f0xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_DeInit), (176 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_PinLockConfig), (34 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadOutputDataBit), (20 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_StructInit), (24 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_WriteBit), (12 bytes).
    Removing stm32f0xx_misc.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_misc.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f0xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ADCCLKConfig), (56 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_AHBPeriphResetCmd), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_AdjustHSI14CalibrationValue), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_BackupResetCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_CECCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_DeInit), (120 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_GetFlagStatus), (72 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_GetITStatus), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HCLKConfig), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSI14ADCRequestCmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSI14Cmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSI48Cmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSICmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_I2CCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_LSEConfig), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_LSEDriveConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_LSICmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_MCOConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PCLKConfig), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PLLCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PREDIV1Config), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_RTCCLKCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_SYSCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_USARTCLKConfig), (72 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_USBCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_WaitForHSEStartUp), (60 bytes).
    Removing stm32f0xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_tim.o(i.TI1_Config), (56 bytes).
    Removing stm32f0xx_tim.o(i.TI2_Config), (76 bytes).
    Removing stm32f0xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f0xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ARRPreloadConfig), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f0xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CCPreloadControl), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC1Ref), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC3Ref), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CounterModeConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CtrlPWMOutputs), (34 bytes).
    Removing stm32f0xx_tim.o(i.TIM_DMACmd), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_DMAConfig), (12 bytes).
    Removing stm32f0xx_tim.o(i.TIM_DeInit), (260 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ETRClockMode1Config), (52 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ETRClockMode2Config), (34 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_EncoderInterfaceConfig), (68 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC1Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC2Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC3Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC4Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetFlagStatus), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ICInit), (112 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_InternalClockConfig), (16 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1Init), (148 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1NPolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2Init), (172 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2NPolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3Init), (152 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3NPolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4Init), (112 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f0xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectCCDMA), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectCOM), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectHallSensor), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectInputTrigger), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectMasterSlaveMode), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOCREFClear), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOnePulseMode), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOutputTrigger), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectSlaveMode), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetClockDivision), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC1Prescaler), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC3Prescaler), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig), (58 bytes).
    Removing stm32f0xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f0xx_tim.o(i.TIM_UpdateDisableConfig), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_UpdateRequestConfig), (28 bytes).
    Removing stm32f0xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_usart.o(i.USART_AddressDetectionConfig), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_AutoBaudRateCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_AutoBaudRateConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_ClockInit), (36 bytes).
    Removing stm32f0xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f0xx_usart.o(i.USART_DECmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_DEPolarityConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_DMAReceptionErrorConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_DataInvCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_DeInit), (240 bytes).
    Removing stm32f0xx_usart.o(i.USART_DirectionModeCmd), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_GetITStatus), (78 bytes).
    Removing stm32f0xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_InvPinCmd), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_LINCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_MSBFirstCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_MuteModeCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_MuteModeWakeUpConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_OneBitMethodCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_OverrunDetectionConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f0xx_usart.o(i.USART_ReceiverTimeOutCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_RequestCmd), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_STOPModeCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_SWAPPinCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetAutoRetryCount), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetBlockLength), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetDEAssertionTime), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetDEDeassertionTime), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetPrescaler), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetReceiverTimeOut), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_StopModeWakeUpSourceConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing hardware.o(.rev16_text), (4 bytes).
    Removing hardware.o(.revsh_text), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(i.delay_ms), (60 bytes).
    Removing delay.o(i.delay_us), (52 bytes).
    Removing usart1.o(.rev16_text), (4 bytes).
    Removing usart1.o(.revsh_text), (4 bytes).
    Removing usart1.o(i.usart1_send_data), (48 bytes).
    Removing usart2.o(.rev16_text), (4 bytes).
    Removing usart2.o(.revsh_text), (4 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing software.o(.rev16_text), (4 bytes).
    Removing software.o(.revsh_text), (4 bytes).

242 unused section(s) (total 7608 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_div0.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    Hardware\Can.c                           0x00000000   Number         0  can.o ABSOLUTE
    Hardware\Delay.c                         0x00000000   Number         0  delay.o ABSOLUTE
    Hardware\Hardware.c                      0x00000000   Number         0  hardware.o ABSOLUTE
    Hardware\Timer.c                         0x00000000   Number         0  timer.o ABSOLUTE
    Hardware\Usart1.c                        0x00000000   Number         0  usart1.o ABSOLUTE
    Hardware\Usart2.c                        0x00000000   Number         0  usart2.o ABSOLUTE
    Hardware\\Can.c                          0x00000000   Number         0  can.o ABSOLUTE
    Hardware\\Delay.c                        0x00000000   Number         0  delay.o ABSOLUTE
    Hardware\\Hardware.c                     0x00000000   Number         0  hardware.o ABSOLUTE
    Hardware\\Timer.c                        0x00000000   Number         0  timer.o ABSOLUTE
    Hardware\\Usart1.c                       0x00000000   Number         0  usart1.o ABSOLUTE
    Hardware\\Usart2.c                       0x00000000   Number         0  usart2.o ABSOLUTE
    Library\\stm32f0xx_can.c                 0x00000000   Number         0  stm32f0xx_can.o ABSOLUTE
    Library\\stm32f0xx_dma.c                 0x00000000   Number         0  stm32f0xx_dma.o ABSOLUTE
    Library\\stm32f0xx_exti.c                0x00000000   Number         0  stm32f0xx_exti.o ABSOLUTE
    Library\\stm32f0xx_gpio.c                0x00000000   Number         0  stm32f0xx_gpio.o ABSOLUTE
    Library\\stm32f0xx_misc.c                0x00000000   Number         0  stm32f0xx_misc.o ABSOLUTE
    Library\\stm32f0xx_rcc.c                 0x00000000   Number         0  stm32f0xx_rcc.o ABSOLUTE
    Library\\stm32f0xx_tim.c                 0x00000000   Number         0  stm32f0xx_tim.o ABSOLUTE
    Library\\stm32f0xx_usart.c               0x00000000   Number         0  stm32f0xx_usart.o ABSOLUTE
    Library\stm32f0xx_can.c                  0x00000000   Number         0  stm32f0xx_can.o ABSOLUTE
    Library\stm32f0xx_dma.c                  0x00000000   Number         0  stm32f0xx_dma.o ABSOLUTE
    Library\stm32f0xx_exti.c                 0x00000000   Number         0  stm32f0xx_exti.o ABSOLUTE
    Library\stm32f0xx_gpio.c                 0x00000000   Number         0  stm32f0xx_gpio.o ABSOLUTE
    Library\stm32f0xx_misc.c                 0x00000000   Number         0  stm32f0xx_misc.o ABSOLUTE
    Library\stm32f0xx_rcc.c                  0x00000000   Number         0  stm32f0xx_rcc.o ABSOLUTE
    Library\stm32f0xx_tim.c                  0x00000000   Number         0  stm32f0xx_tim.o ABSOLUTE
    Library\stm32f0xx_usart.c                0x00000000   Number         0  stm32f0xx_usart.o ABSOLUTE
    Software\Software.c                      0x00000000   Number         0  software.o ABSOLUTE
    Software\\Software.c                     0x00000000   Number         0  software.o ABSOLUTE
    Software\crc16.c                         0x00000000   Number         0  crc16.o ABSOLUTE
    Start\\system_stm32f0xx.c                0x00000000   Number         0  system_stm32f0xx.o ABSOLUTE
    Start\startup_stm32f091.s                0x00000000   Number         0  startup_stm32f091.o ABSOLUTE
    Start\stm32f0xx_it.c                     0x00000000   Number         0  stm32f0xx_it.o ABSOLUTE
    Start\system_stm32f0xx.c                 0x00000000   Number         0  system_stm32f0xx.o ABSOLUTE
    User\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      188  startup_stm32f091.o(RESET)
    !!!main                                  0x080000bc   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000c4   Section       60  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000100   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x0800011c   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000138   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800013a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800013c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800013e   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000140   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000140   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000140   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000146   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000146   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800014a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800014a   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000152   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000154   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000154   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000158   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000160   Section       56  rt_memcpy.o(.emb_text)
    .text                                    0x08000198   Section      116  startup_stm32f091.o(.text)
    .text                                    0x0800020c   Section        0  rt_memcpy.o(.text)
    .text                                    0x0800028e   Section        0  rt_memclr.o(.text)
    .text                                    0x080002ce   Section      346  aeabi_sdiv.o(.text)
    .text                                    0x08000428   Section        0  heapauxi.o(.text)
    .text                                    0x0800042e   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x0800046c   Section        0  exit.o(.text)
    .text                                    0x0800047c   Section        8  libspace.o(.text)
    .text                                    0x08000484   Section        0  sys_exit.o(.text)
    .text                                    0x08000490   Section        2  use_no_semi.o(.text)
    .text                                    0x08000492   Section        0  indicate_semi.o(.text)
    i.CAN1_Receive                           0x08000494   Section        0  can.o(i.CAN1_Receive)
    i.CAN_ClearFlag                          0x080004e0   Section        0  stm32f0xx_can.o(i.CAN_ClearFlag)
    i.CAN_ClearITPendingBit                  0x08000524   Section        0  stm32f0xx_can.o(i.CAN_ClearITPendingBit)
    i.CAN_DeInit                             0x080005e0   Section        0  stm32f0xx_can.o(i.CAN_DeInit)
    i.CAN_FilterInit                         0x080005f8   Section        0  stm32f0xx_can.o(i.CAN_FilterInit)
    i.CAN_GetITStatus                        0x080006f4   Section        0  stm32f0xx_can.o(i.CAN_GetITStatus)
    i.CAN_Init                               0x0800082c   Section        0  stm32f0xx_can.o(i.CAN_Init)
    i.CAN_MessagePending                     0x08000948   Section        0  stm32f0xx_can.o(i.CAN_MessagePending)
    i.CAN_Receive                            0x08000968   Section        0  stm32f0xx_can.o(i.CAN_Receive)
    i.CAN_SendData                           0x08000a70   Section        0  can.o(i.CAN_SendData)
    i.CAN_StructInit                         0x08000ae8   Section        0  stm32f0xx_can.o(i.CAN_StructInit)
    i.CAN_Transmit                           0x08000b08   Section        0  stm32f0xx_can.o(i.CAN_Transmit)
    i.CAN_TransmitStatus                     0x08000c3c   Section        0  stm32f0xx_can.o(i.CAN_TransmitStatus)
    i.CEC_CAN_IRQHandler                     0x08000cec   Section        0  can.o(i.CEC_CAN_IRQHandler)
    i.CheckITStatus                          0x08000d24   Section        0  stm32f0xx_can.o(i.CheckITStatus)
    CheckITStatus                            0x08000d25   Thumb Code    20  stm32f0xx_can.o(i.CheckITStatus)
    i.Crc16                                  0x08000d38   Section        0  crc16.o(i.Crc16)
    i.DMA_ClearFlag                          0x08000d80   Section        0  stm32f0xx_dma.o(i.DMA_ClearFlag)
    i.DMA_Cmd                                0x08000da0   Section        0  stm32f0xx_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x08000dbc   Section        0  stm32f0xx_dma.o(i.DMA_DeInit)
    i.DMA_Enable                             0x08000f20   Section        0  usart1.o(i.DMA_Enable)
    i.DMA_Init                               0x08000f34   Section        0  stm32f0xx_dma.o(i.DMA_Init)
    i.GPIO_Init                              0x08000f74   Section        0  stm32f0xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08001004   Section        0  stm32f0xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ReadInputDataBit                  0x08001048   Section        0  stm32f0xx_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x0800105c   Section        0  stm32f0xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08001060   Section        0  stm32f0xx_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x08001064   Section        0  stm32f0xx_it.o(i.HardFault_Handler)
    i.LED_turn                               0x08001068   Section        0  hardware.o(i.LED_turn)
    i.NMI_Handler                            0x08001094   Section        0  stm32f0xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08001098   Section        0  stm32f0xx_misc.o(i.NVIC_Init)
    i.PendSV_Handler                         0x08001108   Section        0  stm32f0xx_it.o(i.PendSV_Handler)
    i.RCC_AHBPeriphClockCmd                  0x0800110c   Section        0  stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x0800112c   Section        0  stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB1PeriphResetCmd                 0x0800114c   Section        0  stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    i.RCC_APB2PeriphClockCmd                 0x0800116c   Section        0  stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x0800118c   Section        0  stm32f0xx_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x080013d4   Section        0  stm32f0xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080013d8   Section        0  system_stm32f0xx.o(i.SetSysClock)
    SetSysClock                              0x080013d9   Thumb Code   206  system_stm32f0xx.o(i.SetSysClock)
    i.SysTick_CLKSourceConfig                0x080014b0   Section        0  stm32f0xx_misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x080014d4   Section        0  stm32f0xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080014d8   Section        0  system_stm32f0xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08001554   Section        0  timer.o(i.TIM2_IRQHandler)
    i.TIM_ClearITPendingBit                  0x0800159c   Section        0  stm32f0xx_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x080015a4   Section        0  stm32f0xx_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x080015c0   Section        0  stm32f0xx_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x080015e6   Section        0  stm32f0xx_tim.o(i.TIM_ITConfig)
    i.TIM_TimeBaseInit                       0x080015fc   Section        0  stm32f0xx_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x08001678   Section        0  usart1.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080016e4   Section        0  usart2.o(i.USART2_IRQHandler)
    i.USART_ClearFlag                        0x08001778   Section        0  stm32f0xx_usart.o(i.USART_ClearFlag)
    i.USART_ClearITPendingBit                0x0800177c   Section        0  stm32f0xx_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x0800178e   Section        0  stm32f0xx_usart.o(i.USART_Cmd)
    i.USART_DMACmd                           0x080017a6   Section        0  stm32f0xx_usart.o(i.USART_DMACmd)
    i.USART_GetFlagStatus                    0x080017ba   Section        0  stm32f0xx_usart.o(i.USART_GetFlagStatus)
    i.USART_ITConfig                         0x080017ce   Section        0  stm32f0xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001810   Section        0  stm32f0xx_usart.o(i.USART_Init)
    i.USART_OverSampling8Cmd                 0x08001908   Section        0  stm32f0xx_usart.o(i.USART_OverSampling8Cmd)
    i.can_initializes                        0x08001924   Section        0  can.o(i.can_initializes)
    i.delay_init                             0x08001a14   Section        0  delay.o(i.delay_init)
    i.hardware_initializes                   0x08001a20   Section        0  hardware.o(i.hardware_initializes)
    i.main                                   0x08001a70   Section        0  main.o(i.main)
    i.software_initializes                   0x08001acc   Section        0  software.o(i.software_initializes)
    i.timer_initializes                      0x08001b00   Section        0  timer.o(i.timer_initializes)
    i.usart1_initializes                     0x08001b54   Section        0  usart1.o(i.usart1_initializes)
    i.usart2_initializes                     0x08001c30   Section        0  usart2.o(i.usart2_initializes)
    i.usart2_send_data                       0x08001d0c   Section        0  usart2.o(i.usart2_send_data)
    i.usart_DMA_Config_RX                    0x08001d40   Section        0  usart1.o(i.usart_DMA_Config_RX)
    i.usart_DMA_Config_TX                    0x08001d8a   Section        0  usart1.o(i.usart_DMA_Config_TX)
    .constdata                               0x08001dce   Section       32  crc16.o(.constdata)
    .data                                    0x20000000   Section       16  stm32f0xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f0xx_rcc.o(.data)
    .data                                    0x20000010   Section        8  main.o(.data)
    .data                                    0x20000018   Section        1  can.o(.data)
    .data                                    0x20000019   Section        4  timer.o(.data)
    .data                                    0x2000001d   Section        8  software.o(.data)
    .bss                                     0x20000028   Section       20  usart1.o(.bss)
    .bss                                     0x2000003c   Section       20  usart2.o(.bss)
    .bss                                     0x20000050   Section       48  can.o(.bss)
    .bss                                     0x20000080   Section       96  libspace.o(.bss)
    HEAP                                     0x200000e0   Section      512  startup_stm32f091.o(HEAP)
    Heap_Mem                                 0x200000e0   Data         512  startup_stm32f091.o(HEAP)
    STACK                                    0x200002e0   Section     1024  startup_stm32f091.o(STACK)
    Stack_Mem                                0x200002e0   Data        1024  startup_stm32f091.o(STACK)
    __initial_sp                             0x200006e0   Data           0  startup_stm32f091.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000bc   Number         0  startup_stm32f091.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f091.o(RESET)
    __Vectors_End                            0x080000bc   Data           0  startup_stm32f091.o(RESET)
    __main                                   0x080000bd   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000c5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000c5   Thumb Code    52  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000c5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080000d5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000101   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x0800011d   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000139   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x0800013d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000141   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000141   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000141   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000147   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000147   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800014b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800014b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000153   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000155   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000155   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000159   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    __aeabi_memcpy4                          0x08000161   Thumb Code    56  rt_memcpy.o(.emb_text)
    __aeabi_memcpy8                          0x08000161   Thumb Code     0  rt_memcpy.o(.emb_text)
    Reset_Handler                            0x08000199   Thumb Code    38  startup_stm32f091.o(.text)
    ADC1_COMP_IRQHandler                     0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    DMA1_Ch1_IRQHandler                      0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    DMA1_Ch2_3_DMA2_Ch1_2_IRQHandler         0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    DMA1_Ch4_7_DMA2_Ch3_5_IRQHandler         0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    EXTI0_1_IRQHandler                       0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    EXTI2_3_IRQHandler                       0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    EXTI4_15_IRQHandler                      0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    FLASH_IRQHandler                         0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    I2C1_IRQHandler                          0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    I2C2_IRQHandler                          0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    PVD_VDDIO2_IRQHandler                    0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    RCC_CRS_IRQHandler                       0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    RTC_IRQHandler                           0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    SPI1_IRQHandler                          0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    SPI2_IRQHandler                          0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TIM14_IRQHandler                         0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TIM15_IRQHandler                         0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TIM16_IRQHandler                         0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TIM17_IRQHandler                         0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TIM1_BRK_UP_TRG_COM_IRQHandler           0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TIM1_CC_IRQHandler                       0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TIM3_IRQHandler                          0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TIM6_DAC_IRQHandler                      0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TIM7_IRQHandler                          0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    TSC_IRQHandler                           0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    USART3_8_IRQHandler                      0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    WWDG_IRQHandler                          0x080001c9   Thumb Code     0  startup_stm32f091.o(.text)
    __user_initial_stackheap                 0x080001cd   Thumb Code     0  startup_stm32f091.o(.text)
    __aeabi_memcpy                           0x0800020d   Thumb Code   130  rt_memcpy.o(.text)
    __rt_memcpy                              0x0800020d   Thumb Code     0  rt_memcpy.o(.text)
    _memset_w                                0x0800028f   Thumb Code    26  rt_memclr.o(.text)
    _memset                                  0x080002a9   Thumb Code    30  rt_memclr.o(.text)
    __aeabi_memclr                           0x080002c7   Thumb Code     4  rt_memclr.o(.text)
    __rt_memclr                              0x080002c7   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x080002cb   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr8                          0x080002cb   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr_w                            0x080002cb   Thumb Code     4  rt_memclr.o(.text)
    __aeabi_uidiv                            0x080002cf   Thumb Code     0  aeabi_sdiv.o(.text)
    __aeabi_uidivmod                         0x080002cf   Thumb Code    20  aeabi_sdiv.o(.text)
    __aeabi_idiv                             0x080002e3   Thumb Code     0  aeabi_sdiv.o(.text)
    __aeabi_idivmod                          0x080002e3   Thumb Code   326  aeabi_sdiv.o(.text)
    __use_two_region_memory                  0x08000429   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800042b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800042d   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x0800042f   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x0800046d   Thumb Code    16  exit.o(.text)
    __user_libspace                          0x0800047d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800047d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800047d   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08000485   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000491   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000491   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000493   Thumb Code     0  indicate_semi.o(.text)
    CAN1_Receive                             0x08000495   Thumb Code    64  can.o(i.CAN1_Receive)
    CAN_ClearFlag                            0x080004e1   Thumb Code    64  stm32f0xx_can.o(i.CAN_ClearFlag)
    CAN_ClearITPendingBit                    0x08000525   Thumb Code   184  stm32f0xx_can.o(i.CAN_ClearITPendingBit)
    CAN_DeInit                               0x080005e1   Thumb Code    24  stm32f0xx_can.o(i.CAN_DeInit)
    CAN_FilterInit                           0x080005f9   Thumb Code   244  stm32f0xx_can.o(i.CAN_FilterInit)
    CAN_GetITStatus                          0x080006f5   Thumb Code   306  stm32f0xx_can.o(i.CAN_GetITStatus)
    CAN_Init                                 0x0800082d   Thumb Code   280  stm32f0xx_can.o(i.CAN_Init)
    CAN_MessagePending                       0x08000949   Thumb Code    32  stm32f0xx_can.o(i.CAN_MessagePending)
    CAN_Receive                              0x08000969   Thumb Code   262  stm32f0xx_can.o(i.CAN_Receive)
    CAN_SendData                             0x08000a71   Thumb Code   110  can.o(i.CAN_SendData)
    CAN_StructInit                           0x08000ae9   Thumb Code    32  stm32f0xx_can.o(i.CAN_StructInit)
    CAN_Transmit                             0x08000b09   Thumb Code   308  stm32f0xx_can.o(i.CAN_Transmit)
    CAN_TransmitStatus                       0x08000c3d   Thumb Code   150  stm32f0xx_can.o(i.CAN_TransmitStatus)
    CEC_CAN_IRQHandler                       0x08000ced   Thumb Code    48  can.o(i.CEC_CAN_IRQHandler)
    Crc16                                    0x08000d39   Thumb Code    62  crc16.o(i.Crc16)
    DMA_ClearFlag                            0x08000d81   Thumb Code    22  stm32f0xx_dma.o(i.DMA_ClearFlag)
    DMA_Cmd                                  0x08000da1   Thumb Code    24  stm32f0xx_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x08000dbd   Thumb Code   342  stm32f0xx_dma.o(i.DMA_DeInit)
    DMA_Enable                               0x08000f21   Thumb Code    20  usart1.o(i.DMA_Enable)
    DMA_Init                                 0x08000f35   Thumb Code    58  stm32f0xx_dma.o(i.DMA_Init)
    GPIO_Init                                0x08000f75   Thumb Code   144  stm32f0xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08001005   Thumb Code    68  stm32f0xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ReadInputDataBit                    0x08001049   Thumb Code    20  stm32f0xx_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x0800105d   Thumb Code     4  stm32f0xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08001061   Thumb Code     4  stm32f0xx_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x08001065   Thumb Code     4  stm32f0xx_it.o(i.HardFault_Handler)
    LED_turn                                 0x08001069   Thumb Code    38  hardware.o(i.LED_turn)
    NMI_Handler                              0x08001095   Thumb Code     2  stm32f0xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x08001099   Thumb Code   106  stm32f0xx_misc.o(i.NVIC_Init)
    PendSV_Handler                           0x08001109   Thumb Code     2  stm32f0xx_it.o(i.PendSV_Handler)
    RCC_AHBPeriphClockCmd                    0x0800110d   Thumb Code    28  stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x0800112d   Thumb Code    28  stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB1PeriphResetCmd                   0x0800114d   Thumb Code    28  stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    RCC_APB2PeriphClockCmd                   0x0800116d   Thumb Code    28  stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x0800118d   Thumb Code   554  stm32f0xx_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x080013d5   Thumb Code     2  stm32f0xx_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x080014b1   Thumb Code    32  stm32f0xx_misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x080014d5   Thumb Code     2  stm32f0xx_it.o(i.SysTick_Handler)
    SystemInit                               0x080014d9   Thumb Code   110  system_stm32f0xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x08001555   Thumb Code    66  timer.o(i.TIM2_IRQHandler)
    TIM_ClearITPendingBit                    0x0800159d   Thumb Code     6  stm32f0xx_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x080015a5   Thumb Code    24  stm32f0xx_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x080015c1   Thumb Code    38  stm32f0xx_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x080015e7   Thumb Code    20  stm32f0xx_tim.o(i.TIM_ITConfig)
    TIM_TimeBaseInit                         0x080015fd   Thumb Code    90  stm32f0xx_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x08001679   Thumb Code    88  usart1.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080016e5   Thumb Code   126  usart2.o(i.USART2_IRQHandler)
    USART_ClearFlag                          0x08001779   Thumb Code     4  stm32f0xx_usart.o(i.USART_ClearFlag)
    USART_ClearITPendingBit                  0x0800177d   Thumb Code    18  stm32f0xx_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x0800178f   Thumb Code    24  stm32f0xx_usart.o(i.USART_Cmd)
    USART_DMACmd                             0x080017a7   Thumb Code    20  stm32f0xx_usart.o(i.USART_DMACmd)
    USART_GetFlagStatus                      0x080017bb   Thumb Code    20  stm32f0xx_usart.o(i.USART_GetFlagStatus)
    USART_ITConfig                           0x080017cf   Thumb Code    66  stm32f0xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001811   Thumb Code   226  stm32f0xx_usart.o(i.USART_Init)
    USART_OverSampling8Cmd                   0x08001909   Thumb Code    28  stm32f0xx_usart.o(i.USART_OverSampling8Cmd)
    can_initializes                          0x08001925   Thumb Code   230  can.o(i.can_initializes)
    delay_init                               0x08001a15   Thumb Code    12  delay.o(i.delay_init)
    hardware_initializes                     0x08001a21   Thumb Code    74  hardware.o(i.hardware_initializes)
    main                                     0x08001a71   Thumb Code    76  main.o(i.main)
    software_initializes                     0x08001acd   Thumb Code    36  software.o(i.software_initializes)
    timer_initializes                        0x08001b01   Thumb Code    80  timer.o(i.timer_initializes)
    usart1_initializes                       0x08001b55   Thumb Code   202  usart1.o(i.usart1_initializes)
    usart2_initializes                       0x08001c31   Thumb Code   202  usart2.o(i.usart2_initializes)
    usart2_send_data                         0x08001d0d   Thumb Code    42  usart2.o(i.usart2_send_data)
    usart_DMA_Config_RX                      0x08001d41   Thumb Code    74  usart1.o(i.usart_DMA_Config_RX)
    usart_DMA_Config_TX                      0x08001d8b   Thumb Code    68  usart1.o(i.usart_DMA_Config_TX)
    u16CrcTalbeAbs                           0x08001dce   Data          32  crc16.o(.constdata)
    Region$$Table$$Base                      0x08001df0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001e10   Number         0  anon$$obj.o(Region$$Table)
    read_data_command                        0x20000010   Data           8  main.o(.data)
    j                                        0x20000018   Data           1  can.o(.data)
    timer2_management                        0x20000019   Data           4  timer.o(.data)
    data_management                          0x2000001d   Data           8  software.o(.data)
    usart1_management                        0x20000028   Data          20  usart1.o(.bss)
    usart2_management                        0x2000003c   Data          20  usart2.o(.bss)
    buf1                                     0x20000050   Data          48  can.o(.bss)
    __libspace_start                         0x20000080   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200000e0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000bd

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00001e38, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00001e10, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000bc   Data   RO          169    RESET               startup_stm32f091.o
    0x080000bc   0x080000bc   0x00000008   Code   RO         2149  * !!!main             c_p.l(__main.o)
    0x080000c4   0x080000c4   0x0000003c   Code   RO         2318    !!!scatter          c_p.l(__scatter.o)
    0x08000100   0x08000100   0x0000001a   Code   RO         2320    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0800011a   0x0800011a   0x00000002   PAD
    0x0800011c   0x0800011c   0x0000001c   Code   RO         2322    !!handler_zi        c_p.l(__scatter_zi.o)
    0x08000138   0x08000138   0x00000002   Code   RO         2187    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2201    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2203    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2206    .ARM.Collect$$libinit$$0000000A  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2208    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2210    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2213    .ARM.Collect$$libinit$$00000011  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2215    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2217    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2219    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2221    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2223    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2225    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2227    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2229    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2231    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2233    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2237    .ARM.Collect$$libinit$$0000002C  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2239    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2241    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2243    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000002   Code   RO         2244    .ARM.Collect$$libinit$$00000033  c_p.l(libinit2.o)
    0x0800013c   0x0800013c   0x00000002   Code   RO         2275    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2301    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2303    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2306    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2309    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2311    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2314    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000002   Code   RO         2315    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x08000140   0x08000140   0x00000000   Code   RO         2151    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x08000140   0x08000140   0x00000000   Code   RO         2157    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x08000140   0x08000140   0x00000006   Code   RO         2169    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x08000146   0x08000146   0x00000000   Code   RO         2159    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x08000146   0x08000146   0x00000004   Code   RO         2160    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x0800014a   0x0800014a   0x00000000   Code   RO         2162    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x0800014a   0x0800014a   0x00000008   Code   RO         2163    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x08000152   0x08000152   0x00000002   Code   RO         2192    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x08000154   0x08000154   0x00000000   Code   RO         2248    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x08000154   0x08000154   0x00000004   Code   RO         2249    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x08000158   0x08000158   0x00000006   Code   RO         2250    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0800015e   0x0800015e   0x00000002   PAD
    0x08000160   0x08000160   0x00000038   Code   RO         2137    .emb_text           c_p.l(rt_memcpy.o)
    0x08000198   0x08000198   0x00000074   Code   RO          170    .text               startup_stm32f091.o
    0x0800020c   0x0800020c   0x00000082   Code   RO         2138    .text               c_p.l(rt_memcpy.o)
    0x0800028e   0x0800028e   0x00000040   Code   RO         2141    .text               c_p.l(rt_memclr.o)
    0x080002ce   0x080002ce   0x0000015a   Code   RO         2143    .text               c_p.l(aeabi_sdiv.o)
    0x08000428   0x08000428   0x00000006   Code   RO         2147    .text               c_p.l(heapauxi.o)
    0x0800042e   0x0800042e   0x0000003e   Code   RO         2173    .text               c_p.l(sys_stackheap_outer.o)
    0x0800046c   0x0800046c   0x00000010   Code   RO         2176    .text               c_p.l(exit.o)
    0x0800047c   0x0800047c   0x00000008   Code   RO         2188    .text               c_p.l(libspace.o)
    0x08000484   0x08000484   0x0000000c   Code   RO         2245    .text               c_p.l(sys_exit.o)
    0x08000490   0x08000490   0x00000002   Code   RO         2264    .text               c_p.l(use_no_semi.o)
    0x08000492   0x08000492   0x00000000   Code   RO         2266    .text               c_p.l(indicate_semi.o)
    0x08000492   0x08000492   0x00000002   PAD
    0x08000494   0x08000494   0x0000004c   Code   RO         2022    i.CAN1_Receive      can.o
    0x080004e0   0x080004e0   0x00000044   Code   RO          177    i.CAN_ClearFlag     stm32f0xx_can.o
    0x08000524   0x08000524   0x000000bc   Code   RO          178    i.CAN_ClearITPendingBit  stm32f0xx_can.o
    0x080005e0   0x080005e0   0x00000018   Code   RO          180    i.CAN_DeInit        stm32f0xx_can.o
    0x080005f8   0x080005f8   0x000000fc   Code   RO          182    i.CAN_FilterInit    stm32f0xx_can.o
    0x080006f4   0x080006f4   0x00000138   Code   RO          184    i.CAN_GetITStatus   stm32f0xx_can.o
    0x0800082c   0x0800082c   0x0000011c   Code   RO          189    i.CAN_Init          stm32f0xx_can.o
    0x08000948   0x08000948   0x00000020   Code   RO          190    i.CAN_MessagePending  stm32f0xx_can.o
    0x08000968   0x08000968   0x00000106   Code   RO          192    i.CAN_Receive       stm32f0xx_can.o
    0x08000a6e   0x08000a6e   0x00000002   PAD
    0x08000a70   0x08000a70   0x00000078   Code   RO         2023    i.CAN_SendData      can.o
    0x08000ae8   0x08000ae8   0x00000020   Code   RO          195    i.CAN_StructInit    stm32f0xx_can.o
    0x08000b08   0x08000b08   0x00000134   Code   RO          197    i.CAN_Transmit      stm32f0xx_can.o
    0x08000c3c   0x08000c3c   0x000000b0   Code   RO          198    i.CAN_TransmitStatus  stm32f0xx_can.o
    0x08000cec   0x08000cec   0x00000038   Code   RO         2024    i.CEC_CAN_IRQHandler  can.o
    0x08000d24   0x08000d24   0x00000014   Code   RO          200    i.CheckITStatus     stm32f0xx_can.o
    0x08000d38   0x08000d38   0x00000048   Code   RO         2122    i.Crc16             crc16.o
    0x08000d80   0x08000d80   0x00000020   Code   RO          364    i.DMA_ClearFlag     stm32f0xx_dma.o
    0x08000da0   0x08000da0   0x0000001c   Code   RO          366    i.DMA_Cmd           stm32f0xx_dma.o
    0x08000dbc   0x08000dbc   0x00000164   Code   RO          367    i.DMA_DeInit        stm32f0xx_dma.o
    0x08000f20   0x08000f20   0x00000014   Code   RO         1926    i.DMA_Enable        usart1.o
    0x08000f34   0x08000f34   0x00000040   Code   RO          372    i.DMA_Init          stm32f0xx_dma.o
    0x08000f74   0x08000f74   0x00000090   Code   RO          522    i.GPIO_Init         stm32f0xx_gpio.o
    0x08001004   0x08001004   0x00000044   Code   RO          523    i.GPIO_PinAFConfig  stm32f0xx_gpio.o
    0x08001048   0x08001048   0x00000014   Code   RO          526    i.GPIO_ReadInputDataBit  stm32f0xx_gpio.o
    0x0800105c   0x0800105c   0x00000004   Code   RO          529    i.GPIO_ResetBits    stm32f0xx_gpio.o
    0x08001060   0x08001060   0x00000004   Code   RO          530    i.GPIO_SetBits      stm32f0xx_gpio.o
    0x08001064   0x08001064   0x00000004   Code   RO            1    i.HardFault_Handler  stm32f0xx_it.o
    0x08001068   0x08001068   0x0000002c   Code   RO         1850    i.LED_turn          hardware.o
    0x08001094   0x08001094   0x00000002   Code   RO            2    i.NMI_Handler       stm32f0xx_it.o
    0x08001096   0x08001096   0x00000002   PAD
    0x08001098   0x08001098   0x00000070   Code   RO          616    i.NVIC_Init         stm32f0xx_misc.o
    0x08001108   0x08001108   0x00000002   Code   RO            3    i.PendSV_Handler    stm32f0xx_it.o
    0x0800110a   0x0800110a   0x00000002   PAD
    0x0800110c   0x0800110c   0x00000020   Code   RO          652    i.RCC_AHBPeriphClockCmd  stm32f0xx_rcc.o
    0x0800112c   0x0800112c   0x00000020   Code   RO          654    i.RCC_APB1PeriphClockCmd  stm32f0xx_rcc.o
    0x0800114c   0x0800114c   0x00000020   Code   RO          655    i.RCC_APB1PeriphResetCmd  stm32f0xx_rcc.o
    0x0800116c   0x0800116c   0x00000020   Code   RO          656    i.RCC_APB2PeriphClockCmd  stm32f0xx_rcc.o
    0x0800118c   0x0800118c   0x00000248   Code   RO          666    i.RCC_GetClocksFreq  stm32f0xx_rcc.o
    0x080013d4   0x080013d4   0x00000002   Code   RO            4    i.SVC_Handler       stm32f0xx_it.o
    0x080013d6   0x080013d6   0x00000002   PAD
    0x080013d8   0x080013d8   0x000000d8   Code   RO           44    i.SetSysClock       system_stm32f0xx.o
    0x080014b0   0x080014b0   0x00000024   Code   RO          618    i.SysTick_CLKSourceConfig  stm32f0xx_misc.o
    0x080014d4   0x080014d4   0x00000002   Code   RO            5    i.SysTick_Handler   stm32f0xx_it.o
    0x080014d6   0x080014d6   0x00000002   PAD
    0x080014d8   0x080014d8   0x0000007c   Code   RO           46    i.SystemInit        system_stm32f0xx.o
    0x08001554   0x08001554   0x00000048   Code   RO         2064    i.TIM2_IRQHandler   timer.o
    0x0800159c   0x0800159c   0x00000006   Code   RO          927    i.TIM_ClearITPendingBit  stm32f0xx_tim.o
    0x080015a2   0x080015a2   0x00000002   PAD
    0x080015a4   0x080015a4   0x0000001c   Code   RO          932    i.TIM_Cmd           stm32f0xx_tim.o
    0x080015c0   0x080015c0   0x00000026   Code   RO          953    i.TIM_GetITStatus   stm32f0xx_tim.o
    0x080015e6   0x080015e6   0x00000014   Code   RO          957    i.TIM_ITConfig      stm32f0xx_tim.o
    0x080015fa   0x080015fa   0x00000002   PAD
    0x080015fc   0x080015fc   0x0000007c   Code   RO         1005    i.TIM_TimeBaseInit  stm32f0xx_tim.o
    0x08001678   0x08001678   0x0000006c   Code   RO         1927    i.USART1_IRQHandler  usart1.o
    0x080016e4   0x080016e4   0x00000094   Code   RO         1983    i.USART2_IRQHandler  usart2.o
    0x08001778   0x08001778   0x00000004   Code   RO         1490    i.USART_ClearFlag   stm32f0xx_usart.o
    0x0800177c   0x0800177c   0x00000012   Code   RO         1491    i.USART_ClearITPendingBit  stm32f0xx_usart.o
    0x0800178e   0x0800178e   0x00000018   Code   RO         1494    i.USART_Cmd         stm32f0xx_usart.o
    0x080017a6   0x080017a6   0x00000014   Code   RO         1497    i.USART_DMACmd      stm32f0xx_usart.o
    0x080017ba   0x080017ba   0x00000014   Code   RO         1502    i.USART_GetFlagStatus  stm32f0xx_usart.o
    0x080017ce   0x080017ce   0x00000042   Code   RO         1505    i.USART_ITConfig    stm32f0xx_usart.o
    0x08001810   0x08001810   0x000000f8   Code   RO         1506    i.USART_Init        stm32f0xx_usart.o
    0x08001908   0x08001908   0x0000001c   Code   RO         1516    i.USART_OverSampling8Cmd  stm32f0xx_usart.o
    0x08001924   0x08001924   0x000000f0   Code   RO         2025    i.can_initializes   can.o
    0x08001a14   0x08001a14   0x0000000c   Code   RO         1894    i.delay_init        delay.o
    0x08001a20   0x08001a20   0x00000050   Code   RO         1851    i.hardware_initializes  hardware.o
    0x08001a70   0x08001a70   0x0000005c   Code   RO         1798    i.main              main.o
    0x08001acc   0x08001acc   0x00000034   Code   RO         2097    i.software_initializes  software.o
    0x08001b00   0x08001b00   0x00000054   Code   RO         2065    i.timer_initializes  timer.o
    0x08001b54   0x08001b54   0x000000dc   Code   RO         1928    i.usart1_initializes  usart1.o
    0x08001c30   0x08001c30   0x000000dc   Code   RO         1984    i.usart2_initializes  usart2.o
    0x08001d0c   0x08001d0c   0x00000034   Code   RO         1985    i.usart2_send_data  usart2.o
    0x08001d40   0x08001d40   0x0000004a   Code   RO         1930    i.usart_DMA_Config_RX  usart1.o
    0x08001d8a   0x08001d8a   0x00000044   Code   RO         1931    i.usart_DMA_Config_TX  usart1.o
    0x08001dce   0x08001dce   0x00000020   Data   RO         2123    .constdata          crc16.o
    0x08001dee   0x08001dee   0x00000002   PAD
    0x08001df0   0x08001df0   0x00000020   Data   RO         2316    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08001e10, Size: 0x000006e0, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08001e10   0x00000010   Data   RW          692    .data               stm32f0xx_rcc.o
    0x20000010   0x08001e20   0x00000008   Data   RW         1799    .data               main.o
    0x20000018   0x08001e28   0x00000001   Data   RW         2027    .data               can.o
    0x20000019   0x08001e29   0x00000004   Data   RW         2066    .data               timer.o
    0x2000001d   0x08001e2d   0x00000008   Data   RW         2098    .data               software.o
    0x20000025   0x08001e35   0x00000003   PAD
    0x20000028        -       0x00000014   Zero   RW         1932    .bss                usart1.o
    0x2000003c        -       0x00000014   Zero   RW         1986    .bss                usart2.o
    0x20000050        -       0x00000030   Zero   RW         2026    .bss                can.o
    0x20000080        -       0x00000060   Zero   RW         2189    .bss                c_p.l(libspace.o)
    0x200000e0        -       0x00000200   Zero   RW          168    HEAP                startup_stm32f091.o
    0x200002e0        -       0x00000400   Zero   RW          167    STACK               startup_stm32f091.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       492         40          0          1         48       3082   can.o
        72         10         32          0          0       1121   crc16.o
        12          0          0          0          0        437   delay.o
       124         12          0          0          0        990   hardware.o
        92         16          0          8          0       1031   main.o
        52         16          0          8          0       1665   software.o
       116         56        188          0       1536        636   startup_stm32f091.o
      1958         52          0          0          0     175691   stm32f0xx_can.o
       480         34          0          0          0      39370   stm32f0xx_dma.o
       240          0          0          0          0       7687   stm32f0xx_gpio.o
        12          0          0          0          0       3579   stm32f0xx_it.o
       148         10          0          0          0       2310   stm32f0xx_misc.o
       712         46          0         16          0      17007   stm32f0xx_rcc.o
       216         38          0          0          0      23358   stm32f0xx_tim.o
       428         22          0          0          0      15248   stm32f0xx_usart.o
       340         24          0          0          0      78749   system_stm32f0xx.o
       156         10          0          4          0       2236   timer.o
       490         38          0          0         20       4660   usart1.o
       420         50          0          0         20       3270   usart2.o

    ----------------------------------------------------------------------
      6574        <USER>        <GROUP>         40       1624     382127   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          2          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        60          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
       346          0          0          0          0         92   aeabi_sdiv.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        64          0          0          0          0        108   rt_memclr.o
       186          0          0          0          0        144   rt_memcpy.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       868         <USER>          <GROUP>          0         96        892   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       862         16          0          0         96        892   c_p.l

    ----------------------------------------------------------------------
       868         <USER>          <GROUP>          0         96        892   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7442        490        254         40       1720     379875   Grand Totals
      7442        490        254         40       1720     379875   ELF Image Totals
      7442        490        254         40          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 7696 (   7.52kB)
    Total RW  Size (RW Data + ZI Data)              1760 (   1.72kB)
    Total ROM Size (Code + RO Data + RW Data)       7736 (   7.55kB)

==============================================================================

