# 膨胀力气体采集板项目升级总结

## 升级概述
根据说明.md文件的要求，对基于APM32F091RCT6的项目进行了全面升级，实现了四种不同气体浓度读取和四路膨胀力传感器数据读取功能。

## 主要升级内容

### 1. USART配置扩展
原项目只有USART1和USART2，现已扩展到8个USART接口：

- **USART1（膨胀力传感器3）**：TX连接PB7、RX连接PB6
- **USART2（CO传感器）**：TX连接PA2、RX连接PA3
- **USART3（H2传感器）**：TX连接PC4、RX连接PC5
- **USART4（膨胀力传感器1）**：TX连接PC10、RX连接PC11
- **USART5（膨胀力传感器2）**：TX连接PB3、RX连接PB4
- **USART6（膨胀力传感器4）**：TX连接PC0、RX连接PC1
- **USART7（O2传感器）**：TX连接PC6、RX连接PC7
- **USART8（CO2传感器）**：TX连接PC2、RX连接PC3

### 2. 传感器数据处理协议
实现了各种传感器的专用通信协议：

#### CO传感器（USART2）
- 查询指令：11 01 01 ED
- 应答格式：16 05 01 DF1-DF2 DF3-DF4 [CS]
- 浓度计算：CO浓度值=（DF1×256+DF2)/5

#### H2传感器（USART3）
- 查询指令：FF 01 86 00 00 00 00 00 79
- 应答格式：FF 86 00（高位） D1（低位） 05（气体代码） 01（小数位数） 00 00 A3
- 浓度计算：气体浓度值=（气体浓度高位×256+气体浓度低位）×分辨率
- 注：该传感器上电默认主动发送模式

#### 膨胀力传感器（USART1、4、5、6）
- 查询指令：01 03 03 00 00 02 C4 4F
- 应答格式：01 03 04 00 00 03 A7 BB 79
- 数据提取：返回值里的00 00 03 A7就是long型数值，转换成十进制就是935

#### O2传感器（USART7）
- 与H2传感器完全相同的协议和处理方式
- 上电默认主动发送模式

#### CO2传感器（USART8）
- 读浓度指令：FF 03 60 01 00 02 9E 15
- 返回数据：FF 03 04 00（高位） 00（低位） 77 B5 03 BB
- 读小数点个数指令：FF 03 20 31 00 01 CB DB
- 返回数据：FF 03 02 00（高位） 01（低位） 50 50
- 浓度计算：小数点个数为1，浓度要除以10

### 3. CAN数据传输
重新设计了CAN数据传输格式，按要求的顺序打包数据：

- **第一帧（ID: 0x20A）**：气体浓度数据
  - CO浓度（2字节）+ H2浓度（2字节）+ O2浓度（2字节）+ CO2浓度（2字节）

- **第二帧（ID: 0x20B）**：膨胀力1和膨胀力2数据
  - 膨胀力1（4字节）+ 膨胀力2（4字节）

- **第三帧（ID: 0x20C）**：膨胀力3和膨胀力4数据
  - 膨胀力3（4字节）+ 膨胀力4（4字节）

### 4. LED指示灯系统
配置了9个LED指示灯，每当对应设备完成一次收发数据时LED闪烁一次：

- **LED1（膨胀力传感器3）**：PB9
- **LED2（CO传感器）**：PA4
- **LED3（H2传感器）**：PB0
- **LED4（膨胀力传感器1）**：PA15
- **LED5（膨胀力传感器2）**：PD2
- **LED6（膨胀力传感器4）**：PC15-OSC32_OUT
- **LED7（O2传感器）**：PC8
- **LED8（CO2传感器）**：PA0
- **LED CAN**：PB12

LED控制逻辑：高电平熄灭，低电平点亮，闪烁持续50ms。

### 5. 数据管理结构优化
扩展了数据管理结构体，支持新的数据格式：

```c
typedef struct
{
    uint16_t co_concentration;      // CO浓度
    uint16_t h2_concentration;      // H2浓度  
    uint16_t o2_concentration;      // O2浓度
    uint16_t co2_concentration;     // CO2浓度
    uint32_t expansion_force1;      // 膨胀力1
    uint32_t expansion_force2;      // 膨胀力2
    uint32_t expansion_force3;      // 膨胀力3
    uint32_t expansion_force4;      // 膨胀力4
    uint8_t concentration[8];       // 兼容原有代码
}data_management_t;
```

### 6. 中断处理优化
优化了中断处理结构：
- USART1和USART2：独立中断处理
- USART3和USART4：共享USART3_4_IRQHandler
- USART5-8：共享USART3_8_IRQHandler

### 7. 主程序逻辑
重新设计了主程序逻辑：
- 100ms周期：预留给其他任务
- 900ms周期：发送传感器查询命令，对应LED闪烁
- 1000ms周期：通过CAN发送数据，CAN LED闪烁

## 新增文件列表
- Hardware/Usart3.h/c - H2传感器驱动
- Hardware/Usart4.h/c - 膨胀力传感器1驱动
- Hardware/Usart5.h/c - 膨胀力传感器2驱动
- Hardware/Usart6.h/c - 膨胀力传感器4驱动
- Hardware/Usart7.h/c - O2传感器驱动
- Hardware/Usart8.h/c - CO2传感器驱动

## 修改文件列表
- Software/Software.h - 扩展数据结构和管理结构体
- Software/Software.c - 添加新的管理结构体变量
- Hardware/Hardware.h - 添加新的头文件包含和函数声明
- Hardware/Hardware.c - 添加所有USART初始化和LED配置
- Hardware/Usart1.c - 修改为膨胀力传感器3驱动
- Hardware/Usart2.c - 修改为CO传感器驱动
- User/main.c - 重新设计主程序逻辑

## 兼容性说明
- 保持了原有代码的兼容性
- 原有的data_management.concentration[8]数组仍然可用
- 原有的LED_turn()函数仍然可用，现在控制CAN LED

## 注意事项
1. Start和Library目录按要求未做修改
2. H2和O2传感器上电默认主动发送模式，可以不发送查询指令
3. 所有GPIO配置按照说明文档的要求进行
4. CAN数据发送采用多帧格式，确保数据完整性
5. LED闪烁时间设置为50ms，可根据需要调整

## 测试建议
1. 逐个测试每个USART接口的通信功能
2. 验证各传感器数据解析的正确性
3. 测试CAN数据发送的完整性和时序
4. 验证LED指示灯的工作状态
5. 进行长时间稳定性测试
