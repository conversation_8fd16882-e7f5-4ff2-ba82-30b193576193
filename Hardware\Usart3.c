#include "Usart3.h"

usart3_management_t usart3_management;

/******************************************************************
 * @brief  初始化串口3，配置中断及DMA进行数据接收和发送
 *         H2传感器：TX连接PC4、RX连接PC5
 * @input  无
 * @return 无
******************************************************************/
void usart3_initializes(void)
{
	USART_InitTypeDef USART_InitStructure;
	GPIO_InitTypeDef  GPIO_InitStructure;
	NVIC_InitTypeDef  NVIC_InitStructure;
	
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART3, ENABLE);
	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOC, ENABLE);

	GPIO_PinAFConfig(GPIOC, GPIO_PinSource4, GPIO_AF_1);
	GPIO_PinAFConfig(GPIOC, GPIO_PinSource5, GPIO_AF_1);

	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_5;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOC, &GPIO_InitStructure);

	USART_OverSampling8Cmd(USART3, ENABLE);
	USART_InitStructure.USART_BaudRate = 9600;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART3, &USART_InitStructure);
    
	/* usart 3 */
	NVIC_InitStructure.NVIC_IRQChannel = USART3_4_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);

	USART_ITConfig(USART3, USART_IT_IDLE, ENABLE);
	USART_DMACmd(USART3, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);
	USART_Cmd(USART3, ENABLE);
	USART_ClearFlag(USART3, USART_FLAG_TC);
	
	usart_DMA_Config_TX(DMA1_Channel2, (uint32_t)&USART3->TDR, (uint32_t)usart3_management.sendBuffer, USART3_SEND_BUFFER_SIZE);
	usart_DMA_Config_RX(DMA1_Channel3, (uint32_t)&USART3->RDR, (uint32_t)usart3_management.receiveBuffer, USART3_RECEIVE_BUFFER_SIZE);
}

/******************************************************************
 * @brief  通过DMA发送数据
 * @input  data：数据
 *         length：数据长度
 * @return 无
******************************************************************/
void usart3_send_data(uint8_t *data, uint8_t len)
{
	DMA_ClearFlag(DMA1_FLAG_TC2);
	DMA_Cmd(DMA1_Channel2, DISABLE);
	memcpy(usart3_management.sendBuffer, data, len);
	DMA_Enable(DMA1_Channel2, len);
}

/******************************************************************
 * @brief  USART3和USART4共享中断处理函数
 * @input  无
 * @return 无
******************************************************************/
void USART3_4_IRQHandler(void)
{
	// 处理USART3中断
	if (USART_GetFlagStatus(USART3, USART_FLAG_IDLE) != RESET)
	{
		DMA_Cmd(DMA1_Channel3, DISABLE);
		DMA_Enable(DMA1_Channel3, USART3_RECEIVE_BUFFER_SIZE);

		USART_ClearITPendingBit(USART3, USART_IT_IDLE);

        // H2传感器数据处理：FF 86 00（高位） D1（低位） 05（气体代码） 01（小数位数） 00 00 A3
        if(usart3_management.receiveBuffer[0] == 0xFF && usart3_management.receiveBuffer[1] == 0x86)
		{
			uint16_t h2_value = (usart3_management.receiveBuffer[2] << 8) | usart3_management.receiveBuffer[3];
			uint8_t decimal_places = usart3_management.receiveBuffer[5];

			// 计算实际浓度值
			for(uint8_t i = 0; i < decimal_places; i++)
			{
				h2_value *= 10;
			}

			data_management.h2_concentration = h2_value;

			// 兼容原有代码格式
			data_management.concentration[2] = usart3_management.receiveBuffer[2];
			data_management.concentration[3] = usart3_management.receiveBuffer[3];
		}

        memset(usart3_management.receiveBuffer, 0, sizeof(usart3_management.receiveBuffer));
	}

	// 处理USART4中断
	if (USART_GetFlagStatus(USART4, USART_FLAG_IDLE) != RESET)
	{
		DMA_Cmd(DMA1_Channel6, DISABLE);
		DMA_Enable(DMA1_Channel6, USART4_RECEIVE_BUFFER_SIZE);

		USART_ClearITPendingBit(USART4, USART_IT_IDLE);

        // 膨胀力传感器数据处理：01 03 04 00 00 03 A7 BB 79
        if(usart4_management.receiveBuffer[0] == 0x01 && usart4_management.receiveBuffer[1] == 0x03 &&
		   usart4_management.receiveBuffer[2] == 0x04)
		{
			// 提取4字节long型数值：00 00 03 A7
			data_management.expansion_force1 = ((uint32_t)usart4_management.receiveBuffer[3] << 24) |
											   ((uint32_t)usart4_management.receiveBuffer[4] << 16) |
											   ((uint32_t)usart4_management.receiveBuffer[5] << 8) |
											   ((uint32_t)usart4_management.receiveBuffer[6]);
		}

        memset(usart4_management.receiveBuffer, 0, sizeof(usart4_management.receiveBuffer));
	}
}
