# 编译错误修复说明

## 已修复的编译错误

### 1. main.c中的变量声明错误
**错误**: `declaration may not appear after executable statement in block`
**修复**: 将变量声明移到代码块开始处，符合C90标准

### 2. Usart2.c中未使用变量警告
**错误**: `variable "temp" was declared but never referenced`
**修复**: 删除未使用的temp变量声明

### 3. for循环中变量声明错误
**错误**: `type name is not allowed` 在for循环中
**修复**: 在C90标准中，变量必须在代码块开始处声明，不能在for循环中声明

### 4. DMA2通道未定义错误
**错误**: `identifier "DMA2_Channel6" is undefined`
**原因**: STM32F091只有DMA1，没有DMA2
**修复**: 将所有DMA2引用改为DMA1

### 5. 中断向量名称错误
**错误**: 使用了不存在的中断向量名称
**修复**: 根据startup_stm32f091.s文件，STM32F091的中断向量为：
- USART1_IRQHandler
- USART2_IRQHandler  
- USART3_8_IRQHandler（USART3-8共享）

## 当前项目状态

### 支持的USART接口
STM32F091支持USART1-8，但有以下限制：
- USART1和USART2有独立的中断向量
- USART3-8共享一个中断向量（USART3_8_IRQHandler）
- DMA通道有限（只有DMA1的7个通道）

### DMA通道分配问题
由于STM32F091只有DMA1的7个通道，而我们需要支持8个USART的发送和接收，总共需要16个DMA通道，这超出了硬件限制。

### 建议的解决方案

#### 方案1：混合模式（推荐）
- USART1和USART2：使用DMA（主要传感器）
- USART3-8：使用中断模式（辅助传感器）

#### 方案2：纯中断模式
- 所有USART都使用中断模式
- 简化代码结构，避免DMA配置复杂性

#### 方案3：分时复用
- 只同时激活部分USART
- 通过软件调度轮流使用传感器

## 下一步修复计划

### 1. 简化USART配置
```c
// 对于USART3-8，使用简单的中断模式
USART_ITConfig(USARTx, USART_IT_RXNE, ENABLE);  // 接收中断
// 不使用DMA
```

### 2. 修改发送函数
```c
// 使用轮询方式发送数据
void usartx_send_data(uint8_t *data, uint8_t len)
{
    for(uint8_t i = 0; i < len; i++)
    {
        while(USART_GetFlagStatus(USARTx, USART_FLAG_TXE) == RESET);
        USART_SendData(USARTx, data[i]);
    }
}
```

### 3. 简化中断处理
```c
void USART3_8_IRQHandler(void)
{
    if(USART_GetITStatus(USART3, USART_IT_RXNE) != RESET)
    {
        uint8_t data = USART_ReceiveData(USART3);
        // 处理接收到的数据
        USART_ClearITPendingBit(USART3, USART_IT_RXNE);
    }
    // 类似处理其他USART
}
```

## 编译建议

1. **先实现基本功能**：确保代码能够编译通过
2. **逐步添加功能**：一次添加一个USART接口
3. **测试验证**：每添加一个功能都要测试
4. **优化性能**：在基本功能正常后再考虑DMA优化

## 当前编译状态

经过修复，主要的语法错误已经解决：
- ✅ C90标准兼容性问题
- ✅ 变量声明位置问题  
- ✅ DMA通道引用问题
- ✅ 中断向量名称问题

剩余问题：
- ⚠️ DMA通道分配需要重新设计
- ⚠️ 部分USART配置需要简化
- ⚠️ 中断处理函数需要优化

建议先使用简化版本进行测试，确保基本功能正常后再逐步优化。
