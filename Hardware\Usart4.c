#include "Usart4.h"

usart4_management_t usart4_management;

/******************************************************************
 * @brief  初始化串口4，配置中断及DMA进行数据接收和发送
 *         膨胀力传感器1：TX连接PC10、RX连接PC11
 * @input  无
 * @return 无
******************************************************************/
void usart4_initializes(void)
{
	USART_InitTypeDef USART_InitStructure;
	GPIO_InitTypeDef  GPIO_InitStructure;
	NVIC_InitTypeDef  NVIC_InitStructure;
	
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART4, ENABLE);
	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOC, ENABLE);

	GPIO_PinAFConfig(GPIOC, GPIO_PinSource10, GPIO_AF_0);
	GPIO_PinAFConfig(GPIOC, GPIO_PinSource11, GPIO_AF_0);

	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10 | GPIO_Pin_11;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOC, &GPIO_InitStructure);

	USART_OverSampling8Cmd(USART4, ENABLE);
	USART_InitStructure.USART_BaudRate = 9600;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
	USART_Init(USART4, &USART_InitStructure);
    
	/* usart 4 */
	NVIC_InitStructure.NVIC_IRQChannel = USART3_4_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelPriority = 0;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_Init(&NVIC_InitStructure);

	USART_ITConfig(USART4, USART_IT_IDLE, ENABLE);
	USART_DMACmd(USART4, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);
	USART_Cmd(USART4, ENABLE);
	USART_ClearFlag(USART4, USART_FLAG_TC);
	
	usart_DMA_Config_TX(DMA1_Channel7, (uint32_t)&USART4->TDR, (uint32_t)usart4_management.sendBuffer, USART4_SEND_BUFFER_SIZE);
	usart_DMA_Config_RX(DMA1_Channel6, (uint32_t)&USART4->RDR, (uint32_t)usart4_management.receiveBuffer, USART4_RECEIVE_BUFFER_SIZE);
}

/******************************************************************
 * @brief  通过DMA发送数据
 * @input  data：数据
 *         length：数据长度
 * @return 无
******************************************************************/
void usart4_send_data(uint8_t *data, uint8_t len)
{
	DMA_ClearFlag(DMA1_FLAG_TC7);
	DMA_Cmd(DMA1_Channel7, DISABLE);
	memcpy(usart4_management.sendBuffer, data, len);
	DMA_Enable(DMA1_Channel7, len);
}

// USART4中断处理在USART3_4_IRQHandler中统一处理
